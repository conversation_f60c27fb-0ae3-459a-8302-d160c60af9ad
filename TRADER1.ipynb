#IMPORT SHOONYA API AND LOGIN
import sys
sys.path.append("C:/Users/<USER>/Downloads/study/trade/Untitled Folder/ShoonyaApi-py/dist/NorenRestApi-0.0.28")
import NorenRestApiPy
from NorenRestApiPy.NorenApi import  NorenApi
from threading import Timer
import pandas as pd
import time
import concurrent.futures

api = None
class Order:
     def __init__(self, buy_or_sell:str = None, product_type:str = None,
                 exchange: str = None, tradingsymbol:str =None, 
                 price_type: str = None, quantity: int = None, 
                 price: float = None,trigger_price:float = None, discloseqty: int = 0,
                 retention:str = 'DAY', remarks: str = "tag",
                 order_id:str = None):
        self.buy_or_sell=buy_or_sell
        self.product_type=product_type
        self.exchange=exchange
        self.tradingsymbol=tradingsymbol
        self.quantity=quantity
        self.discloseqty=discloseqty
        self.price_type=price_type
        self.price=price
        self.trigger_price=trigger_price
        self.retention=retention
        self.remarks=remarks
        self.order_id=None


    #print(ret)

    


def get_time(time_string):
    data = time.strptime(time_string,'%d-%m-%Y %H:%M:%S')

    return time.mktime(data)


class ShoonyaApiPy(NorenApi):
    def __init__(self):
        NorenApi.__init__(self, host='https://api.shoonya.com/NorenWClientTP/', websocket='wss://api.shoonya.com/NorenWSTP/')        
        global api
        api = self

    def place_basket(self, orders):

        resp_err = 0
        resp_ok  = 0
        result   = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:

            future_to_url = {executor.submit(self.place_order, order): order for order in  orders}
            for future in concurrent.futures.as_completed(future_to_url):
                url = future_to_url[future]
            try:
                result.append(future.result())
            except Exception as exc:
                print(exc)
                resp_err = resp_err + 1
            else:
                resp_ok = resp_ok + 1

        return result
                
    def placeOrder(self,order: Order):
        ret = NorenApi.place_order(self, buy_or_sell=order.buy_or_sell, product_type=order.product_type,
                            exchange=order.exchange, tradingsymbol=order.tradingsymbol, 
                            quantity=order.quantity, discloseqty=order.discloseqty, price_type=order.price_type, 
                            price=order.price, trigger_price=order.trigger_price,
                            retention=order.retention, remarks=order.remarks)
        #print(ret)

        return ret
    
api = ShoonyaApiPy()
# 🔐 Secure Credential Loading with OTP Popup
# Import the secure authentication module
ret=''
from secure_auth import get_secure_credentials

# Load credentials securely (will prompt for OTP)
print("Loading credentials securely...")
creds = get_secure_credentials()

if creds:
    user = creds['user']
    pwd = creds['pwd']
    factor2 = creds['factor2']  # This comes from the OTP popup
    vc = creds['vc']
    apikey = creds['apikey']
    imei = creds['imei']
    print("✅ All credentials loaded successfully!")
    print(f"👤 User: {user}")
    print(f"🏢 Vendor Code: {vc}")
    print(f"📱 IMEI: {imei}")
    print("🔑 Password and API Key: [HIDDEN FOR SECURITY]")
    print("🔐 OTP: [ENTERED SECURELY]")
else:
    print("❌ Failed to load credentials. Please check your cred.yml file.")
    print("Make sure all fields are properly filled out (except factor2).")
    # Stop execution if credentials failed
    raise Exception("Credential loading failed")

ret = api.login(userid=user, password=pwd, twoFA=factor2, vendor_code=vc, api_secret=apikey, imei=imei)
print(ret)




# Enhanced Options with Real-time Symbol Download
import requests
import zipfile
import io
from datetime import datetime

def download_symbols(url, filename):
    """Download and extract symbols file from zip URL"""
    response = requests.get(url, timeout=30)
    response.raise_for_status()
    
    with zipfile.ZipFile(io.BytesIO(response.content)) as zip_ref:
        with zip_ref.open(filename) as file:
            return file.read().decode('utf-8')

def get_expiry_dates(content, exchange='NFO'):
    """Extract available expiry dates from symbols content"""
    raw_dates = set()
    
    for line in content.strip().split('\n'):
        row = line.strip().split(',')
        if len(row) > 6 and row[0] == exchange:
            try:
                if exchange == 'NFO' and len(row) > 6 and row[6] == 'OPTSTK':
                    # For NFO, expiry date is in column 5
                    date_str = row[5].strip()
                    raw_dates.add(date_str)
                elif exchange == 'MCX' and len(row) > 7:
                    # For MCX: Column structure is different
                    # Column 6: Expiry, Column 7: Instrument, Column 8: OptionType
                    if 'OPTFUT' in str(row[7]).upper() and len(row) > 6:
                        date_str = row[6].strip()  # Expiry is in column 6
                        raw_dates.add(date_str)
            except (IndexError, ValueError) as e:
                # Skip invalid rows
                continue
    
    # Filter and sort dates safely
    valid_dates = []
    invalid_dates = []
    
    for date_str in raw_dates:
        # Validate each date string thoroughly
        if is_valid_date(date_str):
            try:
                parsed_date = datetime.strptime(date_str, "%d-%b-%Y")
                valid_dates.append((date_str, parsed_date))
            except ValueError as e:
                invalid_dates.append(date_str)
        else:
            invalid_dates.append(date_str)
    
    # Show what was filtered out for debugging
    if invalid_dates and len(invalid_dates) < 10:  # Only show if reasonable number
        print(f"🗑️ Filtered out {len(invalid_dates)} invalid dates: {invalid_dates[:5]}")
    elif invalid_dates:
        print(f"🗑️ Filtered out {len(invalid_dates)} invalid date entries")
    
    # Return sorted date strings (safe sorting)
    return [date_str for date_str, _ in sorted(valid_dates, key=lambda x: x[1])]

def is_valid_date(date_str):
    """Check if string is a valid date in DD-MMM-YYYY format"""
    if not date_str or len(date_str) < 9:
        return False
    
    # Skip strings that look like trading symbols
    if any(char.isdigit() and char.isalpha() for char in date_str.replace('-', '')):
        # If it has mixed letters and numbers without proper separation, it's likely a symbol
        if not date_str.count('-') == 2:
            return False
    
    try:
        # Check basic format pattern
        parts = date_str.split('-')
        if len(parts) != 3:
            return False
        
        day, month, year = parts
        
        # More strict validation
        if not (day.isdigit() and 1 <= len(day) <= 2 and 1 <= int(day) <= 31):
            return False
        if not (month.isalpha() and len(month) == 3 and month.upper() in 
               ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 
                'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC']):
            return False
        if not (year.isdigit() and len(year) == 4 and 2020 <= int(year) <= 2030):
            return False
        
        # Try to parse it
        datetime.strptime(date_str, "%d-%b-%Y")
        return True
        
    except (ValueError, AttributeError):
        return False


# NSE FUTURE OPTIONS LIST FETCHING USING INTERACTIVE NFO TRADER
# 🎛️ Advanced Widget-based Interactive Options Trading
try:
    import ipywidgets as widgets
    from IPython.display import display, clear_output
    import asyncio
    import threading
    import time
    WIDGETS_AVAILABLE = True
except ImportError:
    WIDGETS_AVAILABLE = False
    print("⚠️ ipywidgets not available. Using standard input method.")

class InteractiveOptionsTrader:
    def __init__(self, api):
        self.api = api
        self.results = set()
        self.processing_complete = False
        
    def validate_percentage(self, percent_value, option_type):
        """Validate percentage input based on option type"""
        if option_type == "ATM" and percent_value != 0:
            return False, "❌ ATM requires 0% (no offset from current price)"
        elif option_type == "ATM" and percent_value == 0:
            return True, "✅ ATM: 0% offset"
        elif percent_value < 0:
            return False, f"❌ Negative % not allowed for {option_type}"
        elif percent_value == 0 and option_type != "ATM":
            return False, f"❌ 0% not valid for {option_type} (use ATM instead)"
        else:
            return True, f"✅ {option_type}: {percent_value}% offset"
    
    def get_current_results(self):
        """Get current results from the trader"""
        return self.results.copy()
    
    def process_options_optimized(self, content, expiry, option_type, ce_percent, pe_percent, exchange='NFO'):
        """Optimized processing with pre-filtering and reduced API calls"""
        self.results = set()
        self.processing_complete = False
        
        # Pre-filter and organize data for faster processing
        tokens_data = {}
        lines = content.strip().split('\n')
        
        print("🔍 Pre-filtering option data...")
        for line in lines:
            row = line.strip().split(',')
            if len(row) > 8 and row[0] == exchange:
                if exchange == 'NFO' and row[5] == expiry and row[6] == 'OPTSTK':
                    token = row[3]
                    if token not in tokens_data:
                        tokens_data[token] = {'CE': [], 'PE': []}
                    
                    option_symbol = row[4]
                    strike = float(row[8])
                    option_call_put = row[7]
                    
                    tokens_data[token][option_call_put].append({
                        'symbol': option_symbol,
                        'strike': strike
                    })
                elif exchange == 'MCX' and len(row) > 9 and row[6] == expiry and 'OPTFUT' in str(row[7]).upper():
                    try:
                        # MCX structure: [Exchange, Token, LotSize, GNGD, Symbol, TradingSymbol, Expiry, Instrument, OptionType, StrikePrice]
                        token = row[4].strip()  # Symbol is in column 4
                        if not token:
                            continue
                            
                        if token not in tokens_data:
                            tokens_data[token] = {'CE': [], 'PE': []}
                        
                        option_symbol = row[5].strip()  # TradingSymbol is in column 5
                        option_call_put = row[8].strip() if len(row) > 8 else ''  # OptionType is in column 8
                        
                        # Validate option type
                        if option_call_put not in ['CE', 'PE']:
                            continue
                        
                        # Parse strike price safely (column 9)
                        try:
                            strike = float(row[9]) if len(row) > 9 and row[9].strip() else 0
                        except (ValueError, IndexError):
                            strike = 0
                        
                        if strike > 0:  # Only add options with valid strike prices
                            tokens_data[token][option_call_put].append({
                                'symbol': option_symbol,
                                'strike': strike
                            })
                    except (IndexError, ValueError, AttributeError) as e:
                        # Skip invalid MCX rows
                        continue
        
        total_tokens = len(tokens_data)
        print(f"✅ Pre-filtered: {total_tokens} unique tokens")
        
        if total_tokens == 0:
            print("❌ No tokens found for selected criteria")
            self.processing_complete = True
            return
        
        processed_count = 0
        options_found = 0
        
        print(f"\n🚀 Starting optimized processing...")
        print("=" * 60)
        
        for token, options in tokens_data.items():
            processed_count += 1
            try:
                # Get current price
                search_exchange = 'NSE' if exchange == 'NFO' else 'MCX'
                search_text = token + '-EQ' if exchange == 'NFO' else token
                
                ret1 = self.api.searchscrip(exchange=search_exchange, searchtext=search_text)
                if not ret1 or 'values' not in ret1 or len(ret1['values']) == 0:
                    print(f"   ⚪ [{processed_count}/{total_tokens}] {token} - No data found")
                    continue
                    
                tokenid = ret1['values'][0]['token']
                ret = self.api.get_quotes(exchange=search_exchange, token=tokenid)
                price = float(ret['lp'])
                
                print(f"📊 [{processed_count}/{total_tokens}] {token} - Price: ₹{price:.2f}")
                
                # Calculate target strikes based on option type and percentages
                if option_type == "ATM":
                    ce_target_strike = price  # Closest to current price
                    pe_target_strike = price  # Closest to current price
                    tolerance = price * 0.01  # 1% tolerance for ATM
                elif option_type == "OTM":
                    ce_target_strike = price * (1 + ce_percent / 100)  # % above current price
                    pe_target_strike = price * (1 - pe_percent / 100)  # % below current price
                    tolerance = price * 0.02  # 2% tolerance
                else:  # ITM
                    ce_target_strike = price * (1 - ce_percent / 100)  # % below current price
                    pe_target_strike = price * (1 + pe_percent / 100)  # % above current price
                    tolerance = price * 0.02  # 2% tolerance
                
                ce_added = pe_added = False
                token_options = 0
                
                # Find CE option closest to target strike
                if options['CE']:
                    ce_options = options['CE']
                    best_ce = None
                    min_ce_diff = float('inf')
                    
                    for ce_option in ce_options:
                        strike = ce_option['strike']
                        
                        # Check if strike is in the right direction for the option type
                        if option_type == "OTM" and strike <= price:
                            continue  # OTM CE must be above current price
                        elif option_type == "ITM" and strike >= price:
                            continue  # ITM CE must be below current price
                        
                        # Find closest to target
                        diff = abs(strike - ce_target_strike)
                        if diff < min_ce_diff and diff <= tolerance:
                            min_ce_diff = diff
                            best_ce = ce_option
                    
                    if best_ce:
                        self.results.add(best_ce['symbol'])
                        ce_added = True
                        token_options += 1
                        options_found += 1
                        actual_percent = ((best_ce['strike'] - price) / price) * 100
                        print(f"   ✅ CE: {best_ce['symbol']} (₹{best_ce['strike']:.0f}, {actual_percent:+.1f}%) | Total: {options_found}")
                
                # Find PE option closest to target strike
                if options['PE']:
                    pe_options = options['PE']
                    best_pe = None
                    min_pe_diff = float('inf')
                    
                    for pe_option in pe_options:
                        strike = pe_option['strike']
                        
                        # Check if strike is in the right direction for the option type
                        if option_type == "OTM" and strike >= price:
                            continue  # OTM PE must be below current price
                        elif option_type == "ITM" and strike <= price:
                            continue  # ITM PE must be above current price
                        
                        # Find closest to target
                        diff = abs(strike - pe_target_strike)
                        if diff < min_pe_diff and diff <= tolerance:
                            min_pe_diff = diff
                            best_pe = pe_option
                    
                    if best_pe:
                        self.results.add(best_pe['symbol'])
                        pe_added = True
                        token_options += 1
                        options_found += 1
                        actual_percent = ((best_pe['strike'] - price) / price) * 100
                        print(f"   ✅ PE: {best_pe['symbol']} (₹{best_pe['strike']:.0f}, {actual_percent:+.1f}%) | Total: {options_found}")
                
                if token_options == 0:
                    print(f"   ⚪ No suitable {option_type} options found")
                    
            except Exception as e:
                print(f"   ❌ Error: {str(e)[:50]}...")
                continue
        
        print("=" * 60)
        print(f"🎉 Processing complete!")
        print(f"📈 Total {exchange} {option_type} options found: {len(self.results)}")
        print(f"📊 Success rate: {processed_count}/{total_tokens} tokens processed")
        
        self.processing_complete = True

def get_nfo_interactive(api):
    """Complete interactive NFO options trading with widgets"""
    if not WIDGETS_AVAILABLE:
        print("❌ ipywidgets required for interactive mode")
        return InteractiveOptionsTrader(api)  # Return empty trader object
    
    trader = InteractiveOptionsTrader(api)
    
    print("🔄 Downloading NFO symbols...")
    content = download_symbols("https://api.shoonya.com/NFO_symbols.txt.zip", "NFO_symbols.txt")
    dates = get_expiry_dates(content, 'NFO')
    print("✅ NFO symbols downloaded successfully!")
    
    # Create widgets
    expiry_dropdown = widgets.Dropdown(
        options=[(f"{i+1}. {date}", date) for i, date in enumerate(dates)],
        description='NFO Expiry:',
        style={'description_width': 'initial'},
        layout=widgets.Layout(width='300px')
    )
    
    option_type_dropdown = widgets.Dropdown(
        options=['OTM', 'ATM', 'ITM'],
        value='OTM',
        description='Option Type:',
        style={'description_width': 'initial'},
        layout=widgets.Layout(width='200px')
    )
    
    ce_percent_input = widgets.FloatText(
        value=3.0,
        description='CE %:',
        style={'description_width': 'initial'},
        layout=widgets.Layout(width='150px')
    )
    
    pe_percent_input = widgets.FloatText(
        value=3.0,
        description='PE %:',
        style={'description_width': 'initial'},
        layout=widgets.Layout(width='150px')
    )
    
    process_button = widgets.Button(
        description="🚀 Process NFO Options",
        button_style='success',
        layout=widgets.Layout(width='200px')
    )
    
    output_area = widgets.Output()
    
    def on_process_click(b):
        with output_area:
            clear_output(wait=True)
            
            # Validate inputs
            ce_valid, ce_msg = trader.validate_percentage(ce_percent_input.value, option_type_dropdown.value)
            pe_valid, pe_msg = trader.validate_percentage(pe_percent_input.value, option_type_dropdown.value)
            
            print(f"CE Validation: {ce_msg}")
            print(f"PE Validation: {pe_msg}")
            
            if not (ce_valid and pe_valid):
                print("\n❌ Please fix validation errors above")
                return
            
            print(f"\n🎯 Processing {option_type_dropdown.value} options for {expiry_dropdown.value}")
            print(f"📊 CE: {ce_percent_input.value}%, PE: {pe_percent_input.value}%")
            
            # Start processing
            trader.process_options_optimized(
                content, 
                expiry_dropdown.value, 
                option_type_dropdown.value,
                ce_percent_input.value,
                pe_percent_input.value,
                'NFO'
            )
            
            print(f"\n📋 Final Results ({len(trader.results)} options):")
            for i, option in enumerate(sorted(list(trader.results)), 1):
                print(f"  {i}. {option}")
    
    def on_option_type_change(change):
        if change['new'] == 'ATM':
            ce_percent_input.value = 0.0
            pe_percent_input.value = 0.0
            ce_percent_input.disabled = True
            pe_percent_input.disabled = True
        else:
            ce_percent_input.disabled = False
            pe_percent_input.disabled = False
            if ce_percent_input.value == 0.0:
                ce_percent_input.value = 3.0
                pe_percent_input.value = 3.0
    
    option_type_dropdown.observe(on_option_type_change, names='value')
    process_button.on_click(on_process_click)
    
    # Layout
    controls = widgets.VBox([
        widgets.HTML("<h3>🎛️ Interactive NFO Options Trading</h3>"),
        widgets.HBox([expiry_dropdown, option_type_dropdown]),
        widgets.HBox([ce_percent_input, pe_percent_input]),
        process_button,
        output_area
    ])
    
    display(controls)
    return trader

# getting nfo list using nfo trader
nfo_trader = get_nfo_interactive(api)

# Wait for processing to complete and get results
import time
# print("⏳ Waiting for NFO processing to complete...")
# while hasattr(nfo_trader, 'processing_complete') and not nfo_trader.processing_complete:
#     time.sleep(1)

# Create sorted list from NFO trader results
if hasattr(nfo_trader, 'results') and nfo_trader.results:
    nfo_options = nfo_trader.results
    new_list_nfo = sorted(list(nfo_options))
    print(f"✅ NFO Options List Created: {len(new_list_nfo)} options")
    print("📋 NFO Options List:")
    for i, option in enumerate(new_list_nfo[:10], 1):  # Show first 10
        print(f"  {i}. {option}")
    if len(new_list_nfo) > 10:
        print(f"  ... and {len(new_list_nfo) - 10} more options")
    
    print(f"\n🔢 Total NFO Options: {len(new_list_nfo)}")
    print("📊 new_list_nfo variable created successfully!")
else:
    print("⚠️ No NFO options found. Please run the interactive NFO trader first.")
    new_list_nfo = []





# Debug MCX data to understand the structure
def debug_mcx_data():
    """Debug MCX data to understand the structure"""
    import requests
    import zipfile
    import io
    import pandas as pd
    
    try:
        print("🔍 Debugging MCX data structure...")
        response = requests.get("https://api.shoonya.com/MCX_symbols.txt.zip", timeout=30)
        response.raise_for_status()
        
        with zipfile.ZipFile(io.BytesIO(response.content)) as zip_file:
            with zip_file.open("MCX_symbols.txt") as file:
                content = file.read().decode('utf-8', errors='ignore')
        
        lines = content.strip().split('\n')
        headers = [h.strip() for h in lines[0].split(',')]
        
        print(f"\n📋 Headers: {headers}")
        print(f"📊 Total lines: {len(lines)}")
        
        # Sample first few data lines
        print("\n📝 Sample data lines:")
        for i, line in enumerate(lines[1:6], 2):
            values = [v.strip() for v in line.split(',')]
            print(f"Line {i}: {dict(zip(headers, values))}")
        
        # Parse all data
        data = []
        for line in lines[1:]:
            if line.strip():
                values = [v.strip() for v in line.split(',')]
                if len(values) >= len(headers):
                    data.append(dict(zip(headers, values)))
        
        df = pd.DataFrame(data)
        print(f"\n📊 Total records: {len(df)}")
        
        # Check columns
        print(f"📋 Columns: {df.columns.tolist()}")
        
        # Check for options data
        if 'OptionType' in df.columns:
            option_types = df['OptionType'].value_counts()
            print(f"\n🔍 Option Types: {option_types.to_dict()}")
            
            options_df = df[df['OptionType'].isin(['CE', 'PE'])]
            print(f"📈 Options records: {len(options_df)}")
            
            if len(options_df) > 0:
                # Check symbols
                symbols = options_df['Symbol'].value_counts()
                print(f"\n🏷️ Top symbols: {symbols.head(10).to_dict()}")
                
                # Check a specific symbol
                test_symbol = symbols.index[0]
                symbol_data = options_df[options_df['Symbol'] == test_symbol]
                print(f"\n🔍 Sample symbol '{test_symbol}':")
                print(f"   Records: {len(symbol_data)}")
                
                if 'Expiry' in symbol_data.columns:
                    expiries = symbol_data['Expiry'].value_counts()
                    print(f"   Expiries: {expiries.head(5).to_dict()}")
                
                if 'Strike' in symbol_data.columns:
                    strikes = symbol_data['Strike'].value_counts()
                    print(f"   Sample strikes: {list(strikes.head(5).index)}")
        
        return df
        
    except Exception as e:
        print(f"❌ Debug error: {str(e)}")
        return None

# Run debug
debug_df = debug_mcx_data()

# MCX OPTIONS FETCHING
# 🎯 ENHANCED MCX OPTIONS INTERFACE - With Guided Selection & Validation

def create_enhanced_mcx_interface():
    """
    Enhanced MCX Options Interface with:
    ✅ Guided selection of all OTM options
    ✅ Expiry date selection for each MCX option type
    ✅ Max/Min % range display for CE and PE
    ✅ Range validation and warnings
    ✅ Option availability checking before processing
    ✅ Beautiful UI with comprehensive information display
    """
    
    import ipywidgets as widgets
    from IPython.display import display, clear_output, HTML
    import pandas as pd
    import numpy as np
    from datetime import datetime
    
    # Check for existing data
    if 'debug_df' not in globals() or debug_df is None:
        display(HTML("""
        <div style='padding: 20px; background: #f8d7da; color: #721c24; border-radius: 10px; margin: 20px 0;'>
            <h3>❌ MCX Data Not Available</h3>
            <p>Please run the MCX data loading cell first to get the options data.</p>
        </div>
        """))
        return None
    
    # Process MCX data with enhanced analysis
    options_df = debug_df[debug_df['OptionType'].isin(['CE', 'PE'])]
    print(f"📊 Processing {len(options_df)} MCX options records...")
    
    symbol_data = {}
    
    for symbol, group in options_df.groupby('Symbol'):
        try:
            expiries = sorted([str(e) for e in group['Expiry'].dropna().unique() if str(e) != 'nan'])
            if not expiries:
                continue
            
            expiry_info = {}
            for expiry in expiries:
                expiry_group = group[group['Expiry'] == expiry]
                
                # Get CE and PE data separately
                ce_data = expiry_group[expiry_group['OptionType'] == 'CE']
                pe_data = expiry_group[expiry_group['OptionType'] == 'PE']
                
                # Extract strikes
                strikes = []
                for _, row in expiry_group.iterrows():
                    try:
                        strike = float(row['StrikePrice'])
                        if strike > 0:
                            strikes.append(strike)
                    except:
                        continue
                
                if strikes:
                    strikes = sorted(list(set(strikes)))
                    current_price = np.median(strikes)
                    atm_strike = min(strikes, key=lambda x: abs(x - current_price))
                    
                    # Calculate comprehensive ranges
                    itm_ranges = []
                    otm_ranges = []
                    ce_strikes = []
                    pe_strikes = []
                    
                    # Get actual CE and PE strikes
                    for _, row in ce_data.iterrows():
                        try:
                            ce_strikes.append(float(row['StrikePrice']))
                        except:
                            continue
                    
                    for _, row in pe_data.iterrows():
                        try:
                            pe_strikes.append(float(row['StrikePrice']))
                        except:
                            continue
                    
                    ce_strikes = sorted(list(set(ce_strikes)))
                    pe_strikes = sorted(list(set(pe_strikes)))
                    
                    # Calculate ranges with proper CE/PE OTM/ITM logic
                    for strike in strikes:
                        pct = abs((strike - atm_strike) / atm_strike) * 100
                        if 0.5 <= pct <= 20:
                            # For CE options:
                            # - OTM CE: strikes above current price (higher strikes)
                            # - ITM CE: strikes below current price (lower strikes)
                            
                            # For PE options:
                            # - OTM PE: strikes below current price (lower strikes) 
                            # - ITM PE: strikes above current price (higher strikes)
                            
                            ce_available = strike in ce_strikes
                            pe_available = strike in pe_strikes
                            
                            if strike > atm_strike:
                                # Higher strikes: OTM for CE, ITM for PE
                                otm_ranges.append({
                                    'percentage': round(pct, 1),
                                    'strike': strike,
                                    'ce_available': ce_available,  # This is OTM CE
                                    'pe_available': False,  # PE cannot be OTM at higher strikes
                                    'option_type': 'CE_OTM'
                                })
                                itm_ranges.append({
                                    'percentage': round(pct, 1),
                                    'strike': strike,
                                    'ce_available': False,  # CE cannot be ITM at higher strikes
                                    'pe_available': pe_available,  # This is ITM PE
                                    'option_type': 'PE_ITM'
                                })
                            elif strike < atm_strike:
                                # Lower strikes: ITM for CE, OTM for PE
                                itm_ranges.append({
                                    'percentage': round(pct, 1),
                                    'strike': strike,
                                    'ce_available': ce_available,  # This is ITM CE
                                    'pe_available': False,  # PE cannot be ITM at lower strikes
                                    'option_type': 'CE_ITM'
                                })
                                otm_ranges.append({
                                    'percentage': round(pct, 1),
                                    'strike': strike,
                                    'ce_available': False,  # CE cannot be OTM at lower strikes
                                    'pe_available': pe_available,  # This is OTM PE
                                    'option_type': 'PE_OTM'
                                })
                    
                    # Sort ranges by percentage
                    itm_ranges = sorted(itm_ranges, key=lambda x: x['percentage'])
                    otm_ranges = sorted(otm_ranges, key=lambda x: x['percentage'])
                    
                    if itm_ranges or otm_ranges:
                        expiry_info[expiry] = {
                            'strikes': strikes,
                            'current_price': current_price,
                            'atm_strike': atm_strike,
                            'itm_ranges': itm_ranges,
                            'otm_ranges': otm_ranges,
                            'ce_count': len(ce_data),
                            'pe_count': len(pe_data),
                            'ce_strikes': ce_strikes,
                            'pe_strikes': pe_strikes,
                            'min_itm_pct': min([r['percentage'] for r in itm_ranges]) if itm_ranges else 0,
                            'max_itm_pct': max([r['percentage'] for r in itm_ranges]) if itm_ranges else 0,
                            'min_otm_pct': min([r['percentage'] for r in otm_ranges]) if otm_ranges else 0,
                            'max_otm_pct': max([r['percentage'] for r in otm_ranges]) if otm_ranges else 0
                        }
            
            if expiry_info:
                symbol_data[symbol] = {
                    'expiries': expiries,
                    'data': expiry_info
                }
                
        except Exception as e:
            continue
    
    print(f"✅ Successfully processed {len(symbol_data)} MCX symbols")
    
    # Create Enhanced Interface Class
    class EnhancedMCXInterface:
        def __init__(self):
            self.symbol_data = symbol_data
            self.selected_combos = []
            self.validation_warnings = []
            self.create_enhanced_ui()
        
        def create_enhanced_ui(self):
            # Header with comprehensive information
            header_html = f"""
            <div style='text-align: center; padding: 25px; background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); 
                        color: white; border-radius: 20px; margin-bottom: 25px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);'>
                <h2>🎯 Enhanced MCX Options Interface</h2>
                <p style='font-size: 16px; margin: 10px 0;'>
                    📊 {len(self.symbol_data)} Symbols Available | 
                    🎛️ Guided Selection | 
                    ⚠️ Range Validation | 
                    🔍 Availability Checking
                </p>
                <div style='background: rgba(255,255,255,0.1); padding: 10px; border-radius: 10px; margin-top: 15px;'>
                    <strong>✨ Features:</strong> Real-time range analysis • Expiry-wise validation • CE/PE availability • Warning system
                </div>
            </div>
            """
            
            self.header = widgets.HTML(header_html)
            
            # Enhanced Symbol Selector with search capability
            symbol_list = list(self.symbol_data.keys())
            self.symbols = widgets.SelectMultiple(
                options=symbol_list,
                description='📊 MCX Symbols:',
                style={'description_width': '140px'},
                layout=widgets.Layout(width='100%', height='220px', margin='10px 0')
            )
            
            # Symbol search box
            self.symbol_search = widgets.Text(
                placeholder='🔍 Type to search symbols...',
                description='Symbol Search:',
                style={'description_width': '140px'},
                layout=widgets.Layout(width='100%', margin='5px 0')
            )
            
            # Expiry selector with enhanced information
            self.expiry = widgets.Dropdown(
                options=[],
                description='📅 Expiry Date:',
                style={'description_width': '140px'},
                layout=widgets.Layout(width='100%', margin='10px 0')
            )
            
            # Option type with enhanced descriptions
            self.opt_type = widgets.Dropdown(
                options=[
                    ('🎯 ATM (At The Money)', 'ATM'),
                    ('📈 ITM (In The Money)', 'ITM'), 
                    ('📉 OTM (Out of The Money)', 'OTM')
                ],
                value='OTM',
                description='🎛️ Option Type:',
                style={'description_width': '140px'},
                layout=widgets.Layout(width='100%', margin='10px 0')
            )
            
            # CE/PE Selection
            self.ce_pe_selection = widgets.SelectMultiple(
                options=[('📈 Call Options (CE)', 'CE'), ('📉 Put Options (PE)', 'PE')],
                value=['CE', 'PE'],
                description='🎯 Select Options:',
                style={'description_width': '140px'},
                layout=widgets.Layout(width='100%', height='80px', margin='10px 0')
            )
            
            # Enhanced percentage slider with range indicators
            self.percentage = widgets.FloatSlider(
                value=3.0, min=0.5, max=15.0, step=0.1,
                description='📊 % Distance:',
                style={'description_width': '140px'},
                layout=widgets.Layout(width='100%', margin='10px 0'),
                readout_format='.1f'
            )
            
            # Range selector for bulk selection
            self.range_selector = widgets.SelectMultiple(
                options=[],
                description='📊 Available %:',
                style={'description_width': '140px'},
                layout=widgets.Layout(width='100%', height='120px', margin='10px 0')
            )
            
            # Comprehensive information display
            self.info_display = widgets.HTML(
                "<div style='padding: 20px; background: #e3f2fd; border-radius: 15px; margin: 15px 0;'>"
                "<h4>📋 Select symbols to see detailed analysis</h4>"
                "<p>Choose MCX symbols and expiry dates to view available percentage ranges for CE and PE options.</p>"
                "</div>"
            )
            
            # Validation warnings display
            self.warnings_display = widgets.HTML("")
            
            # Action buttons
            self.add_btn = widgets.Button(
                description='➕ Add Selected Configuration',
                button_style='info',
                layout=widgets.Layout(width='100%', height='45px', margin='15px 0'),
                style={'font_weight': 'bold'}
            )
            
            self.clear_btn = widgets.Button(
                description='🗑️ Clear All Selections',
                button_style='warning',
                layout=widgets.Layout(width='48%', height='35px', margin='5px 1%')
            )
            
            self.export_btn = widgets.Button(
                description='📋 Export Configuration',
                button_style='',
                layout=widgets.Layout(width='48%', height='35px', margin='5px 1%')
            )
            
            # Selected combinations display
            self.combo_counter = widgets.HTML(
                "<div style='background: #f8f9fa; padding: 15px; border-radius: 10px; text-align: center; margin: 10px 0;'>"
                "<strong>📝 Selected Combinations: 0</strong>"
                "</div>"
            )
            
            # Processing button with enhanced styling
            self.process_btn = widgets.Button(
                description='🚀 Process All Selected Combinations',
                button_style='success',
                layout=widgets.Layout(width='100%', height='55px', margin='20px 0'),
                style={'font_weight': 'bold', 'font_size': '16px'}
            )
            
            # Results and output
            self.results = widgets.HTML()
            self.output = widgets.Output()
            
            # Event handlers
            self.symbol_search.observe(self.on_symbol_search, 'value')
            self.symbols.observe(self.on_symbols_change, 'value')
            self.expiry.observe(self.on_expiry_change, 'value')
            self.opt_type.observe(self.update_comprehensive_info, 'value')
            self.ce_pe_selection.observe(self.update_comprehensive_info, 'value')
            self.percentage.observe(self.update_comprehensive_info, 'value')
            self.range_selector.observe(self.on_range_selection, 'value')
            
            self.add_btn.on_click(self.add_configuration)
            self.clear_btn.on_click(self.clear_selections)
            self.export_btn.on_click(self.export_configuration)
            self.process_btn.on_click(self.process_with_validation)
            
            # Layout with tabbed organization
            configuration_tab = widgets.VBox([
                widgets.HTML("<h3 style='color: #1976d2; margin: 20px 0 10px 0;'>🔧 Symbol & Expiry Configuration</h3>"),
                self.symbol_search,
                self.symbols,
                self.expiry,
                widgets.HTML("<h3 style='color: #1976d2; margin: 20px 0 10px 0;'>🎛️ Option Parameters</h3>"),
                self.opt_type,
                self.ce_pe_selection,
                self.percentage,
                self.range_selector,
                self.info_display,
                self.warnings_display
            ])
            
            selection_tab = widgets.VBox([
                widgets.HTML("<h3 style='color: #388e3c; margin: 20px 0 10px 0;'>📝 Selection Management</h3>"),
                self.add_btn,
                widgets.HBox([self.clear_btn, self.export_btn]),
                self.combo_counter
            ])
            
            processing_tab = widgets.VBox([
                widgets.HTML("<h3 style='color: #d32f2f; margin: 20px 0 10px 0;'>🚀 Processing & Results</h3>"),
                self.process_btn,
                self.results,
                self.output
            ])
            
            main_container = widgets.VBox([
                self.header,
                configuration_tab,
                selection_tab,
                processing_tab
            ], layout=widgets.Layout(width='100%', padding='20px'))
            
            display(main_container)
            
            # Show initial summary
            self.show_initial_summary()
            
            print("✅ Enhanced MCX Interface Ready!")
            print("💡 Features: Symbol search, range validation, availability checking, and comprehensive warnings")
        
        def show_initial_summary(self):
            """Show initial summary of available MCX data"""
            summary_html = "<div style='background: #e8f5e8; padding: 20px; border-radius: 15px; margin: 15px 0;'>"
            summary_html += "<h4>📊 MCX Options Data Summary</h4>"
            
            total_expiries = 0
            total_ce = 0
            total_pe = 0
            
            summary_html += "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin-top: 15px;'>"
            
            for i, (symbol, info) in enumerate(self.symbol_data.items()):
                if i >= 6:  # Show first 6 symbols
                    break
                
                symbol_expiries = len(info['expiries'])
                total_expiries += symbol_expiries
                
                # Get data from first expiry for summary
                first_expiry = info['expiries'][0]
                exp_data = info['data'][first_expiry]
                total_ce += exp_data['ce_count']
                total_pe += exp_data['pe_count']
                
                summary_html += f"""
                <div style='background: white; padding: 15px; border-radius: 10px; border-left: 4px solid #2196f3;'>
                    <h5 style='margin: 0 0 10px 0; color: #1976d2;'>{symbol}</h5>
                    <div style='font-size: 14px; color: #666;'>
                        📅 {symbol_expiries} expiries<br>
                        💹 Est. Price: {int(exp_data['current_price'])} <br>
                        📊 CE: {exp_data['ce_count']} | PE: {exp_data['pe_count']}<br>
                        📈 OTM: {exp_data['min_otm_pct']:.1f}%-{exp_data['max_otm_pct']:.1f}%
                    </div>
                </div>
                """
            
            if len(self.symbol_data) > 6:
                remaining = len(self.symbol_data) - 6
                summary_html += f"""
                <div style='background: #f0f0f0; padding: 15px; border-radius: 10px; text-align: center; color: #666;'>
                    <strong>+{remaining} more symbols</strong><br>
                    <small>Select symbols above to see details</small>
                </div>
                """
            
            summary_html += "</div>"
            
            # Overall statistics
            summary_html += f"""
            <div style='margin-top: 20px; padding: 15px; background: #cce5ff; border-radius: 10px; color: #004085;'>
                <strong>🏆 Overall Statistics:</strong><br>
                📊 Total Symbols: {len(self.symbol_data)} | 
                📅 Avg Expiries: {total_expiries/len(self.symbol_data):.1f} | 
                📈 Total CE: {total_ce} | 
                📉 Total PE: {total_pe}
            </div>
            """
            
            summary_html += "</div>"
            
            self.info_display.value = summary_html
        
        def on_symbol_search(self, change):
            """Filter symbols based on search text"""
            search_text = change['new'].lower()
            if not search_text:
                self.symbols.options = list(self.symbol_data.keys())
            else:
                filtered_symbols = [sym for sym in self.symbol_data.keys() 
                                 if search_text in sym.lower()]
                self.symbols.options = filtered_symbols
        
        def on_symbols_change(self, change):
            """Handle symbol selection changes"""
            selected = list(change['new'])
            if not selected:
                self.expiry.options = []
                self.range_selector.options = []
                self.show_initial_summary()
                return
            
            # Get all unique expiries for selected symbols
            all_expiries = set()
            for sym in selected:
                if sym in self.symbol_data:
                    all_expiries.update(self.symbol_data[sym]['expiries'])
            
            expiries = sorted(list(all_expiries))
            self.expiry.options = [(f"📅 {exp}", exp) for exp in expiries]
            if expiries:
                self.expiry.value = expiries[0]
            
            self.update_comprehensive_info(change)
        
        def on_expiry_change(self, change):
            """Handle expiry selection changes"""
            self.update_comprehensive_info(change)
        
        def update_comprehensive_info(self, change):
            """Update comprehensive information display"""
            selected_symbols = list(self.symbols.value)
            selected_expiry = self.expiry.value
            option_type = self.opt_type.value
            ce_pe_selected = list(self.ce_pe_selection.value)
            current_percentage = self.percentage.value
            
            if not selected_symbols or not selected_expiry:
                return
            
            # Clear previous warnings
            self.validation_warnings = []
            
            # Build comprehensive analysis
            info_html = f"""
            <div style='background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%); padding: 25px; border-radius: 20px; 
                        margin: 15px 0; border-left: 5px solid #28a745; box-shadow: 0 5px 15px rgba(0,0,0,0.1);'>
                <h4 style='margin-top: 0; color: #2e7d32; display: flex; align-items: center;'>
                    📊 Comprehensive Analysis: {len(selected_symbols)} symbols - {selected_expiry}
                </h4>
                
                <div style='margin: 15px 0; padding: 15px; background: #fff3cd; border-radius: 10px; color: #856404;'>
                    <strong>⚙️ Current Configuration:</strong><br>
                    🎛️ Type: {option_type} | 📊 Distance: {current_percentage}% | 
                    🎯 Options: {', '.join(ce_pe_selected)}
                </div>
            """
            
            # Collect all available ranges for range selector
            all_ranges = set()
            total_ce = 0
            total_pe = 0
            available_symbols = 0
            symbol_details = []
            
            for symbol in selected_symbols:
                if symbol in self.symbol_data and selected_expiry in self.symbol_data[symbol]['data']:
                    data = self.symbol_data[symbol]['data'][selected_expiry]
                    total_ce += data['ce_count']
                    total_pe += data['pe_count']
                    available_symbols += 1
                    
                    # Get relevant ranges based on option type
                    if option_type == 'ITM':
                        ranges = data['itm_ranges']
                        for r in ranges:
                            all_ranges.add(r['percentage'])
                    elif option_type == 'OTM':
                        ranges = data['otm_ranges']
                        for r in ranges:
                            all_ranges.add(r['percentage'])
                    else:  # ATM
                        ranges = [{'percentage': 0, 'strike': data['atm_strike'], 'ce_available': True, 'pe_available': True}]
                    
                    # Check availability for current percentage
                    availability_warning = []
                    if option_type != 'ATM':
                        matching_ranges = [r for r in ranges if abs(r['percentage'] - current_percentage) <= 0.5]
                        if matching_ranges:
                            for match in matching_ranges:
                                if 'CE' in ce_pe_selected and not match['ce_available']:
                                    availability_warning.append(f"CE not available at {match['percentage']:.1f}%")
                                if 'PE' in ce_pe_selected and not match['pe_available']:
                                    availability_warning.append(f"PE not available at {match['percentage']:.1f}%")
                        else:
                            availability_warning.append(f"No strikes available at {current_percentage}%")
                    
                    symbol_details.append({
                        'symbol': symbol,
                        'data': data,
                        'ranges': ranges,
                        'warnings': availability_warning
                    })
            
            # Update range selector
            if option_type != 'ATM':
                sorted_ranges = sorted(list(all_ranges))
                self.range_selector.options = [(f"{r:.1f}%", r) for r in sorted_ranges]
                self.range_selector.disabled = False
            else:
                self.range_selector.options = [("ATM Only", 0)]
                self.range_selector.disabled = True
            
            # Display symbol details
            for detail in symbol_details:
                symbol = detail['symbol']
                data = detail['data']
                ranges = detail['ranges']
                warnings = detail['warnings']
                
                info_html += f"""
                <div style='margin: 15px 0; padding: 20px; background: white; border-radius: 15px; 
                            border-left: 3px solid #007bff; box-shadow: 0 3px 10px rgba(0,0,0,0.1);'>
                    <h5 style='margin: 0 0 15px 0; color: #007bff; font-size: 18px; display: flex; align-items: center;'>
                        🏷️ {symbol}
                        <span style='margin-left: auto; font-size: 14px; color: #666;'>
                            📊 {data['ce_count']} CE | {data['pe_count']} PE
                        </span>
                    </h5>
                    
                    <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 15px; margin-bottom: 15px;'>
                        <div style='background: #e3f2fd; padding: 12px; border-radius: 8px; text-align: center;'>
                            <strong>💹 Est. Price</strong><br>
                            <span style='font-size: 18px; color: #1976d2;'>₹{data['current_price']:,.0f}</span>
                        </div>
                        <div style='background: #f3e5f5; padding: 12px; border-radius: 8px; text-align: center;'>
                            <strong>🎯 ATM Strike</strong><br>
                            <span style='font-size: 18px; color: #7b1fa2;'>{data['atm_strike']:,.0f}</span>
                        </div>
                """
                
                if option_type == 'ITM' and data['itm_ranges']:
                    info_html += f"""
                        <div style='background: #ffebee; padding: 12px; border-radius: 8px; text-align: center;'>
                            <strong>📈 ITM Range</strong><br>
                            <span style='font-size: 16px; color: #d32f2f;'>{data['min_itm_pct']:.1f}% - {data['max_itm_pct']:.1f}%</span>
                        </div>
                    """
                elif option_type == 'OTM' and data['otm_ranges']:
                    info_html += f"""
                        <div style='background: #e8f5e8; padding: 12px; border-radius: 8px; text-align: center;'>
                            <strong>📉 OTM Range</strong><br>
                            <span style='font-size: 16px; color: #388e3c;'>{data['min_otm_pct']:.1f}% - {data['max_otm_pct']:.1f}%</span>
                        </div>
                    """
                
                info_html += "</div>"
                
                # Show available ranges
                if option_type != 'ATM' and ranges:
                    ranges_display = []
                    for r in ranges[:8]:  # Show first 8 ranges
                        availability = []
                        if r['ce_available']:
                            availability.append("CE ✅")
                        else:
                            availability.append("CE ❌")
                        if r['pe_available']:
                            availability.append("PE ✅")
                        else:
                            availability.append("PE ❌")
                        
                        ranges_display.append(f"{r['percentage']:.1f}% ({', '.join(availability)})")
                    
                    ranges_text = ', '.join(ranges_display)
                    if len(ranges) > 8:
                        ranges_text += f" + {len(ranges) - 8} more"
                    
                    color = '#d32f2f' if option_type == 'ITM' else '#388e3c'
                    bg_color = '#ffebee' if option_type == 'ITM' else '#e8f5e8'
                    
                    info_html += f"""
                    <div style='color: {color}; background: {bg_color}; padding: 12px; border-radius: 8px; margin-bottom: 10px;'>
                        <strong>📊 Available {option_type} Ranges:</strong><br>
                        <span style='font-size: 14px;'>{ranges_text}</span>
                    </div>
                    """
                
                # Show warnings if any
                if warnings:
                    self.validation_warnings.extend([f"{symbol}: {w}" for w in warnings])
                    warning_text = '<br>'.join([f"⚠️ {w}" for w in warnings])
                    info_html += f"""
                    <div style='background: #fff3cd; color: #856404; padding: 12px; border-radius: 8px; border-left: 3px solid #ffc107;'>
                        <strong>⚠️ Availability Warnings:</strong><br>
                        {warning_text}
                    </div>
                    """
                
                info_html += "</div>"
            
            # Summary section
            info_html += f"""
                <div style='margin-top: 25px; padding: 20px; background: #d4edda; border-radius: 15px; 
                            border-left: 3px solid #28a745; color: #155724;'>
                    <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>
                        <div><strong>✅ Available Symbols:</strong> {available_symbols}/{len(selected_symbols)}</div>
                        <div><strong>📈 Total CE Options:</strong> {total_ce}</div>
                        <div><strong>📉 Total PE Options:</strong> {total_pe}</div>
                        <div><strong>📊 Unique Ranges:</strong> {len(all_ranges)}</div>
                    </div>
                    
                    <div style='margin-top: 15px; padding: 15px; background: rgba(255,255,255,0.7); border-radius: 10px;'>
                        <strong>💡 Quick Tips:</strong><br>
                        • Use the "Available %" selector to choose from actual available ranges<br>
                        • Check warnings above for option availability issues<br>
                        • Green checkmarks (✅) indicate available options, red crosses (❌) indicate unavailable
                    </div>
                </div>
            </div>
            """
            
            self.info_display.value = info_html
            
            # Update warnings display
            if self.validation_warnings:
                warnings_html = """
                <div style='background: #fff3cd; color: #856404; padding: 20px; border-radius: 15px; 
                            margin: 15px 0; border-left: 4px solid #ffc107;'>
                    <h4 style='margin-top: 0; color: #856404;'>⚠️ Validation Warnings</h4>
                """
                for warning in self.validation_warnings:
                    warnings_html += f"<div style='margin: 8px 0; padding: 8px; background: rgba(255,255,255,0.7); border-radius: 5px;'>⚠️ {warning}</div>"
                
                warnings_html += """
                    <div style='margin-top: 15px; padding: 10px; background: rgba(255,255,255,0.7); border-radius: 5px;'>
                        <strong>💡 Suggestions:</strong> Adjust percentage values or select different expiry dates to resolve warnings.
                    </div>
                </div>
                """
                self.warnings_display.value = warnings_html
            else:
                self.warnings_display.value = ""
        
        def on_range_selection(self, change):
            """Handle range selection from the range selector"""
            selected_ranges = list(change['new'])
            if selected_ranges:
                # Set the percentage slider to the first selected range
                self.percentage.value = selected_ranges[0]
        
        def add_configuration(self, btn):
            """Add current configuration to selected combinations"""
            selected_symbols = list(self.symbols.value)
            selected_expiry = self.expiry.value
            option_type = self.opt_type.value
            ce_pe_selected = list(self.ce_pe_selection.value)
            percentage = self.percentage.value
            
            if not selected_symbols or not selected_expiry or not ce_pe_selected:
                with self.output:
                    print("❌ Please select symbols, expiry date, and option types (CE/PE)")
                return
            
            added_count = 0
            
            for symbol in selected_symbols:
                for opt_type in ce_pe_selected:
                    combo = {
                        'symbol': symbol,
                        'expiry': selected_expiry,
                        'type': option_type,
                        'percentage': percentage,
                        'option_type': opt_type  # CE or PE
                    }
                    
                    # Check if combination already exists
                    if combo not in self.selected_combos:
                        self.selected_combos.append(combo)
                        added_count += 1
            
            # Update counter display
            total = len(self.selected_combos)
            self.combo_counter.value = f"""
            <div style='background: #e8f5e8; padding: 20px; border-radius: 15px; text-align: center; 
                        margin: 15px 0; border-left: 4px solid #28a745; box-shadow: 0 3px 10px rgba(0,0,0,0.1);'>
                <h4 style='margin: 0 0 10px 0; color: #2e7d32;'>📝 Selected Combinations</h4>
                <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;'>
                    <div><strong>Total:</strong> {total}</div>
                    <div><strong>Added:</strong> {added_count}</div>
                    <div><strong>Symbols:</strong> {len(set(c['symbol'] for c in self.selected_combos))}</div>
                    <div><strong>Expiries:</strong> {len(set(c['expiry'] for c in self.selected_combos))}</div>
                </div>
                <div style='margin-top: 10px; padding: 10px; background: rgba(255,255,255,0.7); border-radius: 8px; color: #666;'>
                    <small>Recent: {len(selected_symbols)} symbols × {len(ce_pe_selected)} option types = {added_count} combinations</small>
                </div>
            </div>
            """
            
            with self.output:
                print(f"✅ Added {added_count} combinations ({len(selected_symbols)} symbols × {len(ce_pe_selected)} options)")
                print(f"📊 Total selected: {total} combinations")
        
        def clear_selections(self, btn):
            """Clear all selected combinations"""
            self.selected_combos = []
            self.combo_counter.value = """
            <div style='background: #f8f9fa; padding: 15px; border-radius: 10px; text-align: center; margin: 10px 0;'>
                <strong>📝 Selected Combinations: 0</strong><br>
                <small style='color: #666;'>All selections cleared</small>
            </div>
            """
            with self.output:
                print("🗑️ All selections cleared")
        
        def export_configuration(self, btn):
            """Export current configuration"""
            if not self.selected_combos:
                with self.output:
                    print("❌ No combinations to export")
                return
            
            with self.output:
                print("📋 EXPORT - Selected MCX Options Configuration")
                print("=" * 60)
                
                # Group by symbol and expiry
                grouped = {}
                for combo in self.selected_combos:
                    key = (combo['symbol'], combo['expiry'])
                    if key not in grouped:
                        grouped[key] = []
                    grouped[key].append(combo)
                
                for i, ((symbol, expiry), combos) in enumerate(grouped.items(), 1):
                    print(f"\\n{i}. {symbol} - {expiry}")
                    print("-" * 40)
                    
                    for combo in combos:
                        print(f"   • {combo['type']} {combo['percentage']}% - {combo['option_type']}")
                
                print(f"\\n📊 Total: {len(self.selected_combos)} combinations across {len(grouped)} symbol-expiry pairs")
        
        def process_with_validation(self, btn):
            """Process all selected combinations with comprehensive validation"""
            with self.output:
                clear_output(wait=True)
                
                if not self.selected_combos:
                    print("❌ No combinations selected. Please add combinations first.")
                    return
                
                print(f"🚀 Processing {len(self.selected_combos)} selected combinations...")
                print("🔍 Performing validation and availability checks...")
                print("=" * 80)
                
                results = []
                success_count = 0
                total_options_found = 0
                processing_warnings = []
                
                for i, combo in enumerate(self.selected_combos, 1):
                    symbol = combo['symbol']
                    expiry = combo['expiry']
                    opt_type = combo['type']
                    pct = combo['percentage']
                    ce_pe = combo['option_type']
                    
                    print(f"🔄 [{i:3d}/{len(self.selected_combos)}] {symbol} | {expiry} | {opt_type} {pct}% | {ce_pe}")
                    
                    # Validation checks
                    if symbol not in self.symbol_data:
                        print("     ❌ Symbol data not found")
                        processing_warnings.append(f"{symbol}: Symbol data not available")
                        continue
                    
                    if expiry not in self.symbol_data[symbol]['data']:
                        print("     ❌ No data for expiry date")
                        processing_warnings.append(f"{symbol} {expiry}: No options data for expiry")
                        continue
                    
                    data = self.symbol_data[symbol]['data'][expiry]
                    atm_strike = data['atm_strike']
                    strikes = data['strikes']
                    
                    # Find matching strikes based on option type with correct OTM/ITM logic
                    matching_strikes = []
                    
                    if opt_type == 'ATM':
                        # ATM: Both CE and PE use the same ATM strike
                        if ce_pe in ['CE', 'PE']:
                            matching_strikes = [{'strike': atm_strike, 'percentage': 0, 'option_type': f'{ce_pe}_ATM'}]
                    elif opt_type == 'OTM':
                        # OTM Logic: 
                        # - CE OTM: strikes above current price
                        # - PE OTM: strikes below current price
                        if ce_pe == 'CE':
                            # Look for CE OTM options (higher strikes)
                            for strike_info in data['otm_ranges']:
                                if (abs(strike_info['percentage'] - pct) <= 0.5 and 
                                    strike_info.get('option_type') == 'CE_OTM' and 
                                    strike_info['ce_available']):
                                    matching_strikes.append(strike_info)
                        elif ce_pe == 'PE':
                            # Look for PE OTM options (lower strikes)  
                            for strike_info in data['otm_ranges']:
                                if (abs(strike_info['percentage'] - pct) <= 0.5 and 
                                    strike_info.get('option_type') == 'PE_OTM' and 
                                    strike_info['pe_available']):
                                    matching_strikes.append(strike_info)
                    elif opt_type == 'ITM':
                        # ITM Logic:
                        # - CE ITM: strikes below current price
                        # - PE ITM: strikes above current price
                        if ce_pe == 'CE':
                            # Look for CE ITM options (lower strikes)
                            for strike_info in data['itm_ranges']:
                                if (abs(strike_info['percentage'] - pct) <= 0.5 and 
                                    strike_info.get('option_type') == 'CE_ITM' and 
                                    strike_info['ce_available']):
                                    matching_strikes.append(strike_info)
                        elif ce_pe == 'PE':
                            # Look for PE ITM options (higher strikes)
                            for strike_info in data['itm_ranges']:
                                if (abs(strike_info['percentage'] - pct) <= 0.5 and 
                                    strike_info.get('option_type') == 'PE_ITM' and 
                                    strike_info['pe_available']):
                                    matching_strikes.append(strike_info)
                    
                    if matching_strikes:
                        # Limit to top 3 strikes for cleaner output
                        limited_strikes = matching_strikes[:3]
                        results.append({
                            'combo': combo,
                            'strikes': limited_strikes,
                            'ce_count': data['ce_count'],
                            'pe_count': data['pe_count'],
                            'current_price': data['current_price']
                        })
                        success_count += 1
                        total_options_found += len(limited_strikes)
                        print(f"     ✅ Found {len(limited_strikes)} {ce_pe} strikes")
                    else:
                        print(f"     ❌ No {ce_pe} strikes found at {pct}%")
                        processing_warnings.append(f"{symbol} {expiry}: No {ce_pe} options at {pct}%")
                
                print("\\n" + "=" * 80)
                print("🎉 PROCESSING COMPLETE!")
                print(f"📊 Results Summary:")
                print(f"   ✅ Successful: {success_count}/{len(self.selected_combos)} combinations")
                print(f"   🎯 Total Options Found: {total_options_found}")
                print(f"   ⚠️  Warnings: {len(processing_warnings)}")
                
                if processing_warnings:
                    print("\\n⚠️ Processing Warnings:")
                    for warning in processing_warnings[:10]:  # Show first 10 warnings
                        print(f"   • {warning}")
                    if len(processing_warnings) > 10:
                        print(f"   ... and {len(processing_warnings) - 10} more warnings")
                
                # Generate comprehensive results display
                if results:
                    results_html = f"""
                    <div style='background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 30px; border-radius: 20px; 
                                margin-top: 30px; box-shadow: 0 10px 40px rgba(0,0,0,0.1);'>
                        <h3 style='color: #2c3e50; margin-top: 0; text-align: center; font-size: 24px;'>
                            🎉 MCX Options Processing Results
                        </h3>
                        
                        <div style='text-align: center; margin-bottom: 30px; padding: 20px; background: #d4edda; color: #155724; 
                                    border-radius: 15px; border-left: 4px solid #28a745;'>
                            <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;'>
                                <div><strong>✅ Success Rate:</strong> {success_count}/{len(self.selected_combos)} ({success_count/len(self.selected_combos)*100:.1f}%)</div>
                                <div><strong>🎯 Options Found:</strong> {total_options_found}</div>
                                <div><strong>📊 Symbols Processed:</strong> {len(set(r['combo']['symbol'] for r in results))}</div>
                                <div><strong>📅 Expiries Covered:</strong> {len(set(r['combo']['expiry'] for r in results))}</div>
                            </div>
                        </div>
                    """
                    
                    # Group results by symbol for better organization
                    symbol_results = {}
                    for result in results:
                        symbol = result['combo']['symbol']
                        if symbol not in symbol_results:
                            symbol_results[symbol] = []
                        symbol_results[symbol].append(result)
                    
                    for symbol, symbol_result_list in symbol_results.items():
                        results_html += f"""
                        <div style='margin: 25px 0; padding: 25px; background: white; border-radius: 15px; 
                                    border-left: 4px solid #007bff; box-shadow: 0 5px 20px rgba(0,0,0,0.1);'>
                            <h4 style='margin: 0 0 20px 0; color: #007bff; font-size: 20px; display: flex; align-items: center;'>
                                🏷️ {symbol}
                                <span style='margin-left: auto; font-size: 14px; color: #666; background: #f8f9fa; 
                                            padding: 5px 10px; border-radius: 20px;'>
                                    {len(symbol_result_list)} combinations
                                </span>
                            </h4>
                        """
                        
                        for result in symbol_result_list:
                            combo = result['combo']
                            strikes = result['strikes']
                            
                            results_html += f"""
                            <div style='margin: 15px 0; padding: 20px; background: #f8f9fa; border-radius: 12px; 
                                        border-left: 3px solid #28a745;'>
                                <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; flex-wrap: wrap;'>
                                    <div style='font-weight: bold; color: #495057; font-size: 16px;'>
                                        📅 {combo['expiry']} | {combo['type']} {combo['percentage']}% | {combo['option_type']}
                                    </div>
                                    <div style='color: #6c757d; font-size: 14px;'>
                                        💹 Est. Price: ₹{result['current_price']:,.0f}
                                    </div>
                                </div>
                                
                                <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;'>
                            """
                            
                            for j, strike_info in enumerate(strikes, 1):
                                strike = strike_info['strike']
                                percentage = strike_info['percentage']
                                
                                results_html += f"""
                                    <div style='padding: 15px; background: white; border-radius: 10px; 
                                                border-left: 3px solid #28a745; box-shadow: 0 2px 8px rgba(0,0,0,0.1);'>
                                        <div style='font-weight: bold; color: #2e7d32; font-size: 16px; margin-bottom: 8px;'>
                                            {j}. Strike: {strike:,.0f}
                                        </div>
                                        <div style='color: #666; font-size: 14px; margin-bottom: 8px;'>
                                            📊 Distance: {percentage:.1f}%
                                        </div>
                                        <div style='color: #28a745; font-weight: bold;'>
                                            {combo['option_type']} ✅ Available
                                        </div>
                                    </div>
                                """
                            
                            results_html += "</div></div>"
                        
                        results_html += "</div>"
                    
                    # Add next steps section
                    results_html += f"""
                        <div style='margin-top: 30px; padding: 25px; background: #cce5ff; color: #004085; 
                                    border-radius: 15px; border-left: 4px solid #007bff;'>
                            <h4 style='margin: 0 0 15px 0; display: flex; align-items: center;'>
                                💡 Next Steps & Recommendations
                            </h4>
                            <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;'>
                                <div>
                                    <h5 style='margin: 0 0 10px 0; color: #0056b3;'>🎯 Trading Actions:</h5>
                                    <ul style='margin: 0; padding-left: 20px;'>
                                        <li>Use the strike prices above for your MCX options trades</li>
                                        <li>Verify current market prices before placing orders</li>
                                        <li>Consider market conditions and volatility</li>
                                    </ul>
                                </div>
                                <div>
                                    <h5 style='margin: 0 0 10px 0; color: #0056b3;'>📊 Risk Management:</h5>
                                    <ul style='margin: 0; padding-left: 20px;'>
                                        <li>Set appropriate stop-loss levels</li>
                                        <li>Monitor option Greeks (Delta, Gamma, Theta)</li>
                                        <li>Consider position sizing based on risk tolerance</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div style='margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.7); border-radius: 10px;'>
                                <strong>⚠️ Important Disclaimer:</strong> This analysis is based on available options data. 
                                Always verify current market conditions, option availability, and prices before trading. 
                                Past performance does not guarantee future results.
                            </div>
                        </div>
                    </div>
                    """
                    
                    self.results.value = results_html
                    
                    # Auto-update MCX list after successful processing
                    try:
                        if 'update_mcx_list_from_processed' in globals():
                            print("\\n🔄 Auto-updating MCX options list...")
                            update_mcx_list_from_processed()
                        else:
                            print("\\n💡 Run the MCX list helper cell to enable auto-updates")
                    except Exception as e:
                        print(f"\\n⚠️ Could not auto-update MCX list: {str(e)}")
                    
                else:
                    # No results found
                    no_results_html = f"""
                    <div style='padding: 30px; background: #f8d7da; color: #721c24; border-radius: 20px; margin-top: 30px; 
                                border-left: 4px solid #dc3545; box-shadow: 0 5px 15px rgba(0,0,0,0.1);'>
                        <h4 style='margin: 0 0 20px 0; display: flex; align-items: center;'>
                            ❌ No Options Found
                        </h4>
                        
                        <div style='margin-bottom: 20px;'>
                            <strong>Processed:</strong> {len(self.selected_combos)} combinations<br>
                            <strong>Warnings:</strong> {len(processing_warnings)} issues found
                        </div>
                        
                        <div style='background: #fff3cd; color: #856404; padding: 20px; border-radius: 15px; margin: 20px 0;'>
                            <h5 style='margin: 0 0 15px 0;'>🔧 Troubleshooting Suggestions:</h5>
                            <ul style='margin: 0; padding-left: 20px;'>
                                <li><strong>Adjust Percentages:</strong> Try values within the ranges shown in the symbol analysis</li>
                                <li><strong>Select Different Expiries:</strong> Some expiry dates may have more strike options</li>
                                <li><strong>Check Option Types:</strong> Ensure CE/PE options are available for your selections</li>
                                <li><strong>Expand Tolerance:</strong> Consider strikes within ±1% of your target percentage</li>
                            </ul>
                        </div>
                        
                        <div style='background: rgba(255,255,255,0.7); padding: 15px; border-radius: 10px;'>
                            <strong>💡 Quick Fix:</strong> Review the "Available %" selector in the configuration section 
                            to choose from actual available percentage ranges for your selected symbols.
                        </div>
                    </div>
                    """
                    
                    self.results.value = no_results_html
    
    return EnhancedMCXInterface()

# Create the Enhanced MCX Interface
print("🚀 Creating Enhanced MCX Options Interface...")
print("✨ Features: Guided selection, range validation, comprehensive warnings, and availability checking")
enhanced_mcx = create_enhanced_mcx_interface()



# getting mcx list using mcx trader - ONLY FROM PROCESSED OPTIONS
print("🚀 Creating MCX Options List from Enhanced MCX Interface - PROCESSED OPTIONS ONLY...")

# Initialize MCX options set
mcx_options = set()

# Function to extract processed MCX options
def extract_processed_mcx_options():
    """Extract options from processed MCX results only"""
    processed_options = set()
    
    if 'enhanced_mcx' in globals() and enhanced_mcx:
        print("🔍 Checking enhanced MCX interface for processed results...")
        
        # Method 1: Check if there are processed results stored in the interface
        if hasattr(enhanced_mcx, 'results') and enhanced_mcx.results:
            print("✅ Found processed results in enhanced_mcx.results")
            if hasattr(enhanced_mcx.results, 'value') and enhanced_mcx.results.value:
                print("📊 Results are available in HTML format - results processed!")
                
                # Extract from selected combinations that were successfully processed
                for combo in enhanced_mcx.selected_combos:
                    symbol = combo['symbol']
                    expiry = combo['expiry']
                    option_type = combo['option_type']  # CE or PE
                    percentage = combo['percentage']
                    opt_type = combo['type']  # ATM, ITM, OTM
                    
                    print(f"🔄 Processing: {symbol} | {expiry} | {opt_type} {percentage}% | {option_type}")
                    
                    # Get the actual option symbols from debug_df that match the processed criteria
                    if 'debug_df' in globals() and debug_df is not None:
                        # Find options that match the exact criteria from processed combinations
                        if symbol in enhanced_mcx.symbol_data and expiry in enhanced_mcx.symbol_data[symbol]['data']:
                            data = enhanced_mcx.symbol_data[symbol]['data'][expiry]
                            
                            # Get the target strikes based on the processing logic
                            atm_strike = data['atm_strike']
                            
                            # Find matching strikes from the processed ranges
                            matching_strikes = []
                            
                            if opt_type == 'ATM':
                                matching_strikes = [atm_strike]
                            elif opt_type == 'ITM':
                                for strike_info in data['itm_ranges']:
                                    if abs(strike_info['percentage'] - percentage) <= 0.5:
                                        # Check availability
                                        if ((option_type == 'CE' and strike_info['ce_available']) or 
                                            (option_type == 'PE' and strike_info['pe_available'])):
                                            matching_strikes.append(strike_info['strike'])
                            elif opt_type == 'OTM':
                                for strike_info in data['otm_ranges']:
                                    if abs(strike_info['percentage'] - percentage) <= 0.5:
                                        # Check availability
                                        if ((option_type == 'CE' and strike_info['ce_available']) or 
                                            (option_type == 'PE' and strike_info['pe_available'])):
                                            matching_strikes.append(strike_info['strike'])
                            
                            # Get the actual trading symbols for these strikes
                            for target_strike in matching_strikes:
                                symbol_options = debug_df[
                                    (debug_df['Symbol'] == symbol) & 
                                    (debug_df['Expiry'] == expiry) &
                                    (debug_df['OptionType'] == option_type) &
                                    (debug_df['StrikePrice'].astype(float) == float(target_strike))
                                ]
                                
                                for _, row in symbol_options.iterrows():
                                    try:
                                        trading_symbol = row['TradingSymbol']
                                        if trading_symbol and str(trading_symbol).strip():
                                            processed_options.add(str(trading_symbol).strip())
                                            print(f"  ✅ Added: {trading_symbol}")
                                    except:
                                        continue
            else:
                print("⚠️ No processed results found - processing may not be complete")
                print("💡 Tip: Use the 'Process All Selected Combinations' button in the MCX interface first")
        else:
            print("⚠️ Enhanced MCX interface not found or no results available")
            print("💡 Tip: Run the MCX interface and process some combinations first")
    
    return processed_options

# Extract processed options
print("🔍 Extracting processed MCX options...")
mcx_options = extract_processed_mcx_options()

if len(mcx_options) == 0:
    print("⚠️ No processed MCX options found!")
    print("📋 To create MCX options list:")
    print("   1. Run the Enhanced MCX Interface (cell above)")
    print("   2. Select symbols and configure options")
    print("   3. Add combinations using 'Add Selected Configuration'")
    print("   4. Click 'Process All Selected Combinations'")
    print("   5. Then run this cell again")
    
    # Create empty list as fallback
    new_list_mcx = []
else:
    # Create sorted list from processed MCX options
    new_list_mcx = sorted(list(mcx_options))
    
    print(f"✅ MCX Options List Created: {len(new_list_mcx)} PROCESSED options")
    print("📋 Processed MCX Options List:")
    for i, option in enumerate(new_list_mcx[:15], 1):  # Show first 15
        print(f"  {i}. {option}")
    if len(new_list_mcx) > 15:
        print(f"  ... and {len(new_list_mcx) - 15} more options")

print(f"\n🔢 Total Processed MCX Options: {len(new_list_mcx)}")
print("📊 new_list_mcx variable created successfully!")

# Display summary of both lists
print("\n" + "="*70)
print("📊 SUMMARY - BOTH NFO AND MCX LISTS CREATED")
print("="*70)
print(f"📈 NFO Options (new_list_nfo): {len(new_list_nfo) if 'new_list_nfo' in globals() else 0} symbols")
print(f"📉 MCX Processed Options (new_list_mcx): {len(new_list_mcx)} symbols") 
print(f"🎯 Total Combined Options: {(len(new_list_nfo) if 'new_list_nfo' in globals() else 0) + len(new_list_mcx)}")
print("="*70)
print("💡 Note: MCX list contains only processed options from the Enhanced MCX Interface")
print("   NFO list contains all options from the Interactive NFO Trader")

# new_list_nfo = sorted(list(nfo_options))
print(new_list_nfo)

# new_list_mcx = sorted(list(mcx_options))
print(new_list_mcx)


#Nadarya Watson (check_vander) signal function to see if there is signal in last 3 minutes of selected script and time for MCX and NFO

import pandas as pd
import datetime
import os
import time
import matplotlib.pyplot as plt
import sys
import subprocess
def live_data(data):
    # pairs = "ltcusdt"  # self.pairs
    t_frame = "1min"  # self.time_frame
    import pandas as pd
    from datetime import datetime

    response = data

    time = []
    open_ = []
    high_ = []
    low_ = []
    close_ = []
    volume = []
    # print(data)
    for candle in response:
        time.append(datetime.strptime(candle['time'], '%d-%m-%Y %H:%M:%S'))
        open_.append(float(candle['into']))
        high_.append(float(candle['inth']))
        low_.append(float(candle['intl']))
        close_.append(float(candle['intc']))
        volume.append(float(candle['intv']))

    candles = pd.DataFrame({
        "Open": open_,
        "High": high_,
        "Low": low_,
        "Close": close_,
        "volume": volume,
        "time": time
    })

    candles = candles.set_index("time")
    return candles

import datetime
def get_start_end_timestamps(date_input, starttime_input, endtime_input):
    # Parse the input date
    date_parts = date_input.split('-')
    year, month, day = int(date_parts[2]), int(date_parts[1]), int(date_parts[0])

    # Parse the input times
    start_time = datetime.datetime.strptime(starttime_input, '%H:%M').time()
    end_time = datetime.datetime.strptime(endtime_input, '%H:%M').time()

    # Combine the input date with the input times
    start_datetime = datetime.datetime(year, month, day, start_time.hour, start_time.minute)
    end_datetime = datetime.datetime(year, month, day, end_time.hour, end_time.minute)

    # # Adjust start_datetime and end_datetime if they are in the past
    # current_datetime = datetime.datetime.now()
    # if start_datetime < current_datetime:
    #     start_datetime += datetime.timedelta(days=0)
    # if end_datetime < current_datetime:
    #     end_datetime += datetime.timedelta(days=0)

    # print(start_datetime)
    # print(end_datetime)

    start_timestamp = start_datetime.timestamp()
    end_timestamp = end_datetime.timestamp()

    return start_timestamp, end_timestamp

# # Example usage
# date_input = '23-05-2024'
# starttime_input = '18:35'
# endtime_input = '19:37'

# from datetime import datetime, timedelta

# while True:
#     try:
        # Clear the previous output
        # clear_output(wait=True)
        # os.system('cls')  # 'cls' for Windows systems
        # Clear the previous plot
        # plt.clf()
def check_vander(tokenid, exchange, current=False, date_input =None, starttime_input=None, endtime_input=None):
        # print('inside check vander')
        isvander = False
        # plt.close('all')
         # Clear the output
        # sys.stdout.flush()
        # print("\033[H\033[J", end="")
        # # subprocess.run(['cls'])
        # os.system('cls' if os.name == 'nt' else 'clear')
        

        
        if date_input is None:
        # Get the current date and time
            now = datetime.datetime.now()
            
            # Extract the date, start time, and end time from the current datetime
            date_input = now.date().strftime('%d-%m-%Y')
            endtime_input = now.time().strftime('%H:%M')
            starttime_input = (now - datetime.timedelta(hours=1)).time().strftime('%H:%M')
        else:
            date_input = date_input
            endtime_input = endtime_input
            starttime_input = starttime_input
            # print(f'date_input', date_input)
            # print(f'endtime_input', endtime_input)
            # print(f'starttime_input', starttime_input)
        # date_input = '27-05-2024'
        # starttime_input = '14:20'
        # endtime_input = '15:29'
            
        # print("date_input:", date_input)
        # print("starttime_input:", starttime_input)
        # print("endtime_input:", endtime_input)
        
        start_timestamp, end_timestamp = get_start_end_timestamps(date_input, starttime_input, endtime_input)
        # print(f'Start timestamp: {start_timestamp}')
        # print(f'End timestamp: {end_timestamp}')
        # print()
        # Retry the API call up to 3 times if there is an error
        max_retries = 3
        retries = 0
        while retries < max_retries:
            try:
                # data = api.get_time_price_series(exchange='NSE', token='11872', starttime=start_timestamp, endtime=end_timestamp, interval=1)
                if exchange == 'NFO' or exchange == 'NSE':
                    # print('inside vander2 fetch data')
                    #ver6
                    data = api.get_time_price_series(exchange='NSE', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                    # print(data)
                elif exchange == 'MCX' : 
                    # print(exchange)
                    data = api.get_time_price_series(exchange='MCX', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                    # print(data)
                break  # If the API call is successful, exit the loop
            except Exception as e:
                # print(f"Error: {e}")
                retries += 1
                if retries == max_retries:
                    x=1
                    # print("Maximum number of retries reached. Unable to fetch data.")
                else:
                    # print(f"Retrying in 1 seconds... (Attempt {retries}/{max_retries})")
                    time.sleep(1)
        
        if retries < max_retries:
            new0=0
            # print(data)
        # # data = api.get_time_price_series(exchange='NSE', token='11872', starttime=start_timestamp, endtime=end_timestamp, interval=1)
        # data = api.get_time_price_series(exchange='MCX', token='430171', starttime=start_timestamp, endtime=end_timestamp, interval=1)
        # # response_data_ce = api.get_time_price_series(exchange='MCX', token=token_call, starttime=start_timestamp, endtime=end_timestamp, interval=1)
        # # response_data_pe = api.get_time_price_series(exchange='MCX', token=token_put, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                
        # print(data)
        # current_datetime = datetime.datetime.now()
        # start_datetime = current_datetime - datetime.timedelta(days=0, hours=8, minutes=30)
        # end_datetime = current_datetime - datetime.timedelta(days=0, hours=6, minutes=30)
            
        # start_timestamp = start_datetime.timestamp()
        # end_timestamp = end_datetime.timestamp()
            
        # data = api.get_time_price_series(exchange='NSE', token='11872', starttime=start_timestamp, endtime=end_timestamp, interval=1)
        #         # response_data_pe = api.get_time_price_series(exchange='MCX', token=token_put, starttime=start_timestamp, endtime=end_timestamp, interval=1)
        
        data = live_data(data)
        data = data.sort_values(by='time')
        data1=data
        # print(data)
        
        data = data['Close'].values
        
        import math
        h      = 8
        mult   = 3
        src    = data
        k = 1.75
        y = []
        #..............#
        up = []
        dn = []
        up_signal = []
        dn_signal = []
        up_temp = 0
        dn_temp = 0
        #.................#
        upper_band = []
        lower_band = []
        upper_band_signal = []
        lower_band_signal = []
        #....................#
        sum_e = 0
        for i in range(len(data)):
            sum = 0
            sumw = 0   
            for j in range(len(data)):
                w = math.exp(-(math.pow(i-j,2)/(h*h*2)))
                sum += src[j]*w
                sumw += w
            y2 = sum/sumw
            sum_e += abs(src[i] - y2)
            y.insert(i,y2)
        # mae = sum_e/len(data)*mult
        mae = sum_e/len(data)*k
        #print(mae)
        import numpy as np
        for i  in range(len(data)):
                y2 = y[i]
                y1 = y[i-1]
                
                if y[i]>y[i-1]:
                    up.insert(i,y[i])
                    if up_temp == 0:
                        up_signal.insert(i,data[i])
                    else:
                        up_signal.insert(i,np.nan)
                    up_temp = 1
                else:
                    up_temp = 0
                    up.insert(i,np.nan)
                    up_signal.insert(i,np.nan)
                    
                if y[i]<y[i-1]:
                    dn.insert(i,y[i])
                    if dn_temp == 0:
                        dn_signal.insert(i,data[i])
                    else:
                        dn_signal.insert(i,np.nan)
                    dn_temp = 1
                else:
                    dn_temp = 0
                    dn.insert(i,np.nan)
                    dn_signal.insert(i,np.nan)
                    
                    
                # upper_band.insert(i,y[i]+mae)
                # lower_band.insert(i,y[i]-mae)
                upper_band.insert(i, y[i] + mae * k)  # Modify the upper band calculation
                lower_band.insert(i, y[i] - mae * k)  # Modify the lower band calculation
                if data[i]> upper_band[i]:
                    upper_band_signal.insert(i,data[i])
                else:
                    upper_band_signal.insert(i,np.nan)
                    
                if data[i]<lower_band[i]:
                    lower_band_signal.insert(i,data[i])
                else:
                    lower_band_signal.insert(i,np.nan)
        import pandas as pd
        Nadaraya_Watson = pd.DataFrame({
                    "Buy": up,
                    "Sell": dn,
                    "BUY_Signal": up_signal,
                    "Sell_Signal": dn_signal,
                    "Uppar_Band": upper_band,
                    "Lower_Band":lower_band,
                    "Upper_Band_signal":upper_band_signal,
                    "Lower_Band_Signal":lower_band_signal
                })
        # print(Nadaraya_Watson)
        
        # %matplotlib inline   
        # print('plotting:')
        import matplotlib.pyplot as plt
        plt.figure(figsize=(18,8))
        # fig1, plt2 = plt.subplots(figsize=(15, 8))
        # plt.figure(figsize=(10, 6), dpi=100)
        plt.plot(np.array(upper_band), color= 'green', linestyle='--', linewidth=2) 
        plt.plot(np.array(lower_band), color= 'red', linestyle='--', linewidth=2) 
        
        # plt.plot(np.array(up_signal), color= 'green', marker='^', linestyle='dashed', linewidth=2, markersize=8) 
        # plt.plot(np.array(dn_signal), color= 'red', marker='v', linestyle='dashed', linewidth=2, markersize=8) 
        #plt.plot(np.array(upper_band), color= 'green', label= 'Polynomial model',marker='o', linestyle='dashed', linewidth=2, markersize=12) 
        
        
        plt.plot(np.array(dn), color= 'red', label= 'Polynomial model') 
        plt.plot(np.array(up), color= 'green', label= 'Polynomial model')
        plt.plot(np.array(upper_band_signal), color= 'red', marker='v', linestyle='dashed', linewidth=2, markersize=15) 
        plt.plot(np.array(lower_band_signal), color= 'green', marker='^', linestyle='dashed', linewidth=2, markersize=15) 
        isvander = False
        
        plt.plot(np.array(data[:]), color= 'blue', label= 'Data')
        
        # Set the x-axis ticks and labels using the time column from the data DataFrame
        # plt.xticks(np.arange(len(data1)), data1.index, rotation=45)
        # plt.xlabel('Time')
        
        # Extract time from the index and display every 5th time value
        times = [time.strftime('%H:%M:%S') for time in data1.index[::5]]
        plt.xticks(np.arange(len(data1))[::5], times, rotation=45)
        plt.xlabel('Time')
        if exchange == 'NFO' or exchange == 'NSE':
            ret3 = api.get_quotes(exchange='NSE', token=tokenid)
            name1 = ret3['cname']
            # print(f'eq name:{name1}')
        elif exchange == 'MCX':   
            ret3 = api.get_quotes(exchange='MCX', token=tokenid)
            name1 = ret3['symname']
            # print(ret3)

        minutes_check = -3
    
        # plt.text(f'Overall Trend: {name1}', ha='left', va='bottom', fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
        # plt.text(0.95, 0.05, f'Overall Trend: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
        # Check which signal (upper band or lower band) was present in the last 5 minutes
        upper_band_present_last_5 = any(x is not np.nan for x in upper_band_signal[minutes_check:])
        lower_band_present_last_5 = any(x is not np.nan for x in lower_band_signal[minutes_check:])
        
        if upper_band_present_last_5 and lower_band_present_last_5:
            signal_text = f'Last {minutes_check} mins: Both signals present'
            plt.text(0.8, 0.5, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
            plt.text(0.35, 0.35, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
            isvander = True
            # print(f'eq name:{name1}')
            # plt.show()
            # time.sleep(2)
        elif upper_band_present_last_5:
            signal_text = f'Last {minutes_check} mins: Upper band signal present'
            plt.text(0.95, 0.3, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
            plt.text(0.35, 0.35, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
            isvander = True
            # print(f'eq name:{name1}')
            # plt.show()
            # time.sleep(2)
        elif lower_band_present_last_5:
            signal_text = f'Last {minutes_check} mins: Lower band signal present'
            plt.text(0.95, 0.3, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
            plt.text(0.35, 0.35, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
            isvander = True
            # print(f'eq name:{name1}')
            # plt.show()
            # time.sleep(2)
        else:
            if not any(x is not np.nan for x in upper_band_signal) and not any(x is not np.nan for x in lower_band_signal):
                signal_text = f'No signal present at all in last {minutes_check} minutes'
                plt.text(0.8, 0.5, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
                plt.text(0.35, 0.35, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
                isvander = False
            else:
                signal_text = f'Last {minutes_check} mins: No signal present'
                plt.text(0.8, 0.5, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
                plt.text(0.35, 0.35, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
                
                isvander = False
        # Add text at the bottom right of the plot
        # plt.text(0.95, 0.3, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
        # plt.text(0.95, 0.05, f'Overall Trend: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))

        # plt.show()
        # time.sleep(2)
        # plt.clf()
        # plt.close('all')
        # plt.close()
        return isvander, signal_text
        

    # except Exception as e:
    #     print(f"Error occurred while fetching data: {e}")

    # # Wait for the next minute
    # time.sleep(1)
    # # time.sleep(61 - (datetime.datetime.now().second % 60))

def check_vander1(tokenid, exchange,  current=False, date_input =None, starttime_input=None, endtime_input=None):
        # print('inside check vander1')
        isvander = False
        # plt.close('all')
         # Clear the output
        # sys.stdout.flush()
        # print("\033[H\033[J", end="")
        # # subprocess.run(['cls'])
        # os.system('cls' if os.name == 'nt' else 'clear')
        

        if date_input is None:
        # Get the current date and time
            now = datetime.datetime.now()
            
            # Extract the date, start time, and end time from the current datetime
            date_input = now.date().strftime('%d-%m-%Y')
            endtime_input = now.time().strftime('%H:%M')
            starttime_input = (now - datetime.timedelta(hours=1)).time().strftime('%H:%M')
        else:
            date_input = date_input
            endtime_input = endtime_input
            starttime_input = starttime_input
        # date_input = '27-05-2024'
        # starttime_input = '14:20'
        # endtime_input = '15:29'
            
        # print("date_input:", date_input)
        # print("starttime_input:", starttime_input)
        # print("endtime_input:", endtime_input)
        
        
        
        
        
        
        
        
        start_timestamp, end_timestamp = get_start_end_timestamps(date_input, starttime_input, endtime_input)
        # print(f'Start timestamp: {start_timestamp}')
        # print(f'End timestamp: {end_timestamp}')
        # print()
        # Retry the API call up to 3 times if there is an error
        max_retries = 3
        retries = 0
        while retries < max_retries:
            try:
                # data = api.get_time_price_series(exchange='NSE', token='11872', starttime=start_timestamp, endtime=end_timestamp, interval=1)
                if exchange == 'NFO' or exchange == 'NSE':
                    # print('inside vander1 fetch data')
                    data = api.get_time_price_series(exchange='NSE', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                    # print(data)
                elif exchange == 'MCX' : 
                    data = api.get_time_price_series(exchange='MCX', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                break  # If the API call is successful, exit the loop
            except Exception as e:
                # print(f"Error: {e}")
                retries += 1
                if retries == max_retries:
                    x=1
                    # print("Maximum number of retries reached. Unable to fetch data.")
                else:
                    # print(f"Retrying in 2 seconds... (Attempt {retries}/{max_retries})")
                    time.sleep(1)
        
        if retries < max_retries:
            new0=0
            # print(data)
        # # data = api.get_time_price_series(exchange='NSE', token='11872', starttime=start_timestamp, endtime=end_timestamp, interval=1)
        # data = api.get_time_price_series(exchange='MCX', token='430171', starttime=start_timestamp, endtime=end_timestamp, interval=1)
        # # response_data_ce = api.get_time_price_series(exchange='MCX', token=token_call, starttime=start_timestamp, endtime=end_timestamp, interval=1)
        # # response_data_pe = api.get_time_price_series(exchange='MCX', token=token_put, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                
        # print(data)
        # current_datetime = datetime.datetime.now()
        # start_datetime = current_datetime - datetime.timedelta(days=0, hours=8, minutes=30)
        # end_datetime = current_datetime - datetime.timedelta(days=0, hours=6, minutes=30)
            
        # start_timestamp = start_datetime.timestamp()
        # end_timestamp = end_datetime.timestamp()
            
        # data = api.get_time_price_series(exchange='NSE', token='11872', starttime=start_timestamp, endtime=end_timestamp, interval=1)
        #         # response_data_pe = api.get_time_price_series(exchange='MCX', token=token_put, starttime=start_timestamp, endtime=end_timestamp, interval=1)
        
        data = live_data(data)
        data = data.sort_values(by='time')
        data1=data
        # print(data)
        
        data = data['Close'].values
        
        import math
        h      = 8
        mult   = 3
        src    = data
        k = 1.5
        y = []
        #..............#
        up = []
        dn = []
        up_signal = []
        dn_signal = []
        up_temp = 0
        dn_temp = 0
        #.................#
        upper_band = []
        lower_band = []
        upper_band_signal = []
        lower_band_signal = []
        #....................#
        sum_e = 0
        for i in range(len(data)):
            sum = 0
            sumw = 0   
            for j in range(len(data)):
                w = math.exp(-(math.pow(i-j,2)/(h*h*2)))
                sum += src[j]*w
                sumw += w
            y2 = sum/sumw
            sum_e += abs(src[i] - y2)
            y.insert(i,y2)
        # mae = sum_e/len(data)*mult
        mae = sum_e/len(data)*k
        #print(mae)
        import numpy as np
        for i  in range(len(data)):
                y2 = y[i]
                y1 = y[i-1]
                
                if y[i]>y[i-1]:
                    up.insert(i,y[i])
                    if up_temp == 0:
                        up_signal.insert(i,data[i])
                    else:
                        up_signal.insert(i,np.nan)
                    up_temp = 1
                else:
                    up_temp = 0
                    up.insert(i,np.nan)
                    up_signal.insert(i,np.nan)
                    
                if y[i]<y[i-1]:
                    dn.insert(i,y[i])
                    if dn_temp == 0:
                        dn_signal.insert(i,data[i])
                    else:
                        dn_signal.insert(i,np.nan)
                    dn_temp = 1
                else:
                    dn_temp = 0
                    dn.insert(i,np.nan)
                    dn_signal.insert(i,np.nan)
                    
                    
                # upper_band.insert(i,y[i]+mae)
                # lower_band.insert(i,y[i]-mae)
                upper_band.insert(i, y[i] + mae * k)  # Modify the upper band calculation
                lower_band.insert(i, y[i] - mae * k)  # Modify the lower band calculation
                if data[i]> upper_band[i]:
                    upper_band_signal.insert(i,data[i])
                else:
                    upper_band_signal.insert(i,np.nan)
                    
                if data[i]<lower_band[i]:
                    lower_band_signal.insert(i,data[i])
                else:
                    lower_band_signal.insert(i,np.nan)
        import pandas as pd
        Nadaraya_Watson = pd.DataFrame({
                    "Buy": up,
                    "Sell": dn,
                    "BUY_Signal": up_signal,
                    "Sell_Signal": dn_signal,
                    "Uppar_Band": upper_band,
                    "Lower_Band":lower_band,
                    "Upper_Band_signal":upper_band_signal,
                    "Lower_Band_Signal":lower_band_signal
                })
        # print(Nadaraya_Watson)
        
        # %matplotlib inline    
        # import matplotlib.pyplot as plt
        # plt.figure(figsize=(18,8))
    
        # fig1, plt2 = plt.subplots(figsize=(15, 8))
        # plt.figure(figsize=(10, 6), dpi=100)
    
        # plt.plot(np.array(upper_band), color= 'green', linestyle='--', linewidth=2) 
        # plt.plot(np.array(lower_band), color= 'red', linestyle='--', linewidth=2) 
        
        # plt.plot(np.array(up_signal), color= 'green', marker='^', linestyle='dashed', linewidth=2, markersize=8) 
        # plt.plot(np.array(dn_signal), color= 'red', marker='v', linestyle='dashed', linewidth=2, markersize=8) 
        #plt.plot(np.array(upper_band), color= 'green', label= 'Polynomial model',marker='o', linestyle='dashed', linewidth=2, markersize=12) 
        
        
        # plt.plot(np.array(dn), color= 'red', label= 'Polynomial model') 
        # plt.plot(np.array(up), color= 'green', label= 'Polynomial model')
        # plt.plot(np.array(upper_band_signal), color= 'red', marker='v', linestyle='dashed', linewidth=2, markersize=15) 
        # plt.plot(np.array(lower_band_signal), color= 'green', marker='^', linestyle='dashed', linewidth=2, markersize=15) 
        isvander = False
        
        # plt.plot(np.array(data[:]), color= 'blue', label= 'Data')
        
        # Set the x-axis ticks and labels using the time column from the data DataFrame
        # plt.xticks(np.arange(len(data1)), data1.index, rotation=45)
        # plt.xlabel('Time')
        
        # Extract time from the index and display every 5th time value
        times = [time.strftime('%H:%M:%S') for time in data1.index[::5]]
        # plt.xticks(np.arange(len(data1))[::5], times, rotation=45)
        # plt.xlabel('Time')
        if exchange == 'NFO' or exchange == 'NSE':
            ret3 = api.get_quotes(exchange='NSE', token=tokenid)
            name1 = ret3['cname']
        elif exchange == 'MCX':   
            ret3 = api.get_quotes(exchange='MCX', token=tokenid)
            name1 = ret3['symname']
            # print(ret3)
        
    
        # plt.text(f'Overall Trend: {name1}', ha='left', va='bottom', fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
        # plt.text(0.95, 0.05, f'Overall Trend: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
        # Check which signal (upper band or lower band) was present in the last 5 minutes
        upper_band_present_last_5 = any(x is not np.nan for x in upper_band_signal[-3:])
        lower_band_present_last_5 = any(x is not np.nan for x in lower_band_signal[-3:])
        
        if upper_band_present_last_5 and lower_band_present_last_5:
            signal_text = 'Last 5 mins: Both signals present'
            # plt.text(0.8, 0.5, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
            # plt.text(0.95, 0.05, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
            isvander = True
            # print(f'eq_name:{name1}')
            # plt.show()
            # time.sleep(2)
        elif upper_band_present_last_5:
            signal_text = 'Last 5 mins: Upper band signal present'
            # print(f'eq_name:{name1}')
            isvander = True
            # plt.text(0.95, 0.3, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
            # plt.text(0.95, 0.05, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
            # isvander = True
            # plt.show()
            # time.sleep(2)
        elif lower_band_present_last_5:
            signal_text = 'Last 5 mins: Lower band signal present'
            # plt.text(0.95, 0.3, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
            # plt.text(0.95, 0.05, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
            isvander = True
            # print(f'eq_name:{name1}')
            # plt.show()
            # time.sleep(2)
        else:
            if not any(x is not np.nan for x in upper_band_signal) and not any(x is not np.nan for x in lower_band_signal):
                signal_text = 'No signal present at all'
                # plt.text(0.8, 0.5, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
                # plt.text(0.95, 0.05, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
                isvander = False
            else:
                signal_text = 'Last 5 mins: No signal present'
                # plt.text(0.8, 0.5, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
                # plt.text(0.95, 0.05, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
                
                isvander = False
        # Add text at the bottom right of the plot
        # plt.text(0.95, 0.3, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
        # plt.text(0.95, 0.05, f'Overall Trend: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))

        # plt.show()
        # time.sleep(2)
        # plt.clf()
        # plt.close('all')
        # plt.close()
        # print('dint plot anything')
        return isvander, signal_text
        


# checking higher highs, higher lows, etc to detect the sideways trend during Nadarya Watson (check_vander) signal  to see if there is signal in last 3 minutes of selected script and time for MCX and NFO and the stock is trending sideways

import datetime
from scipy.signal import argrelextrema
import numpy as np
from collections import deque
from matplotlib.lines import Line2D
from datetime import timedelta

def live_data(data):
    # pairs = "ltcusdt"  # self.pairs
    t_frame = "1min"  # self.time_frame
    import pandas as pd
    from datetime import datetime

    response = data

    time = []
    open_ = []
    high_ = []
    low_ = []
    close_ = []
    volume = []

    for candle in response:
        time.append(datetime.strptime(candle['time'], '%d-%m-%Y %H:%M:%S'))
        open_.append(float(candle['into']))
        high_.append(float(candle['inth']))
        low_.append(float(candle['intl']))
        close_.append(float(candle['intc']))
        volume.append(float(candle['intv']))

    candles = pd.DataFrame({
        "Open": open_,
        "High": high_,
        "Low": low_,
        "Close": close_,
        "volume": volume,
        "time": time
    })

    candles = candles.set_index("time")
    return candles

def get_start_end_timestamps(date_input, starttime_input, endtime_input):
    # Parse the input date
    date_parts = date_input.split('-')
    year, month, day = int(date_parts[2]), int(date_parts[1]), int(date_parts[0])

    # Parse the input times
    start_time = datetime.datetime.strptime(starttime_input, '%H:%M').time()
    end_time = datetime.datetime.strptime(endtime_input, '%H:%M').time()

    # Combine the input date with the input times
    start_datetime = datetime.datetime(year, month, day, start_time.hour, start_time.minute)
    end_datetime = datetime.datetime(year, month, day, end_time.hour, end_time.minute)


    start_timestamp = start_datetime.timestamp()
    end_timestamp = end_datetime.timestamp()

    return start_timestamp, end_timestamp

 

import pandas as pd
# import tradingpatterns.tradingpatterns as tp1
import sys


import pandas as pd

import pandas as pd

def find_pivots(df, window_minutes=30):
    """
    Find pivots (higher highs, lower lows, lower highs, and higher lows) in the given DataFrame using non-overlapping windows.

    Args:
        df (pandas.DataFrame): The DataFrame containing 'High', 'Low', and 'Close' columns.
        window_minutes (int): The size of the non-overlapping window in minutes. Default is 30 minutes.

    Returns:
        pandas.DataFrame: The original DataFrame with an additional 'signal' column containing the pivot signals.
    """
    # Convert the index to datetime
    df.index = pd.to_datetime(df.index)
    # print('inside find pivot')
    # print(df)
    # Resample data to the specified window size and calculate the high and low values
    window_size = f'{window_minutes}min'
    resampled_data = df['High'].resample(window_size).max().to_frame('High')
    resampled_data['Low'] = df['Low'].resample(window_size).min()

    # Calculate differences between consecutive highs and lows
    high_diffs = resampled_data['High'].diff()
    low_diffs = resampled_data['Low'].diff()

    # Find higher high
    higher_high_mask = (high_diffs > 0) & (high_diffs.shift(-1) < 0)

    # Find lower low
    lower_low_mask = (low_diffs < 0) & (low_diffs.shift(-1) > 0)

    # Find lower high
    lower_high_mask = (high_diffs < 0) & (high_diffs.shift(-1) > 0)

    # Find higher low
    higher_low_mask = (low_diffs > 0) & (low_diffs.shift(-1) < 0)

    # Create signals column
    resampled_data['signal'] = ''
    resampled_data.loc[higher_high_mask, 'signal'] = 'HH'
    resampled_data.loc[lower_low_mask, 'signal'] = 'LL'
    resampled_data.loc[lower_high_mask, 'signal'] = 'LH'
    resampled_data.loc[higher_low_mask, 'signal'] = 'HL'

    # Merge the resampled DataFrame with the original DataFrame to include the 'Close' column
    result = pd.merge(df, resampled_data[['High', 'Low', 'signal']], left_index=True, right_index=True, how='left')

    return result

    # return resampled_data

# %matplotlib inline
import matplotlib.pyplot as plt1

# def plot_pivots(df):
#     plt.figure(figsize=(12, 6))
    
#     # Plot the data
#     plt.plot(df['Close'], color='black', label='Close Price')

#     print('insie plot pivot')
#     print(df)
#     # Plot pivots
#     pivots = df[df['signal'] != '']
#     for index, row in pivots.iterrows():
#         signal = row['signal']
#         close = row['Close']
#         if signal == 'HH':
#             plt.text(index, close, 'HH', color='green', fontsize=10, ha='center', va='center')
#         elif signal == 'LL':
#             plt.text(index, close, 'LL', color='red', fontsize=10, ha='center', va='center')
#         elif signal == 'LH':
#             plt.text(index, close, 'LH', color='green', fontsize=10, ha='center', va='center')
#         elif signal == 'HL':
#             plt.text(index, close, 'HL', color='red', fontsize=10, ha='center', va='center')
    
    
#     # Set x-axis ticks and labels
#     # times = [time.strftime('%H:%M:%S') for time in df['date']]
#     # Set x-axis ticks and labels
#     # times = [time.strftime('%H:%M:%S') for time in df.index]
#     # plt.xticks(np.arange(len(df))[::5], times, rotation=45)
#     # plt.xticks(np.arange(len(df)), times, rotation=45)
#     plt.xticks(df.index, df.index.strftime('%H:%M:%S'), rotation=45)

#     # Set x-axis ticks and labels
#     # tick_indices = range(0, len(df.index), 4)  # Every 4 minutes
#     # tick_labels = [df.index[i].strftime('%H:%M:%S') for i in tick_indices]
#     # plt.xticks(tick_indices, tick_labels, rotation=45)

    
#     plt.xlabel('Time')
#     plt.ylabel('Price')
#     plt.title('Pivot Points')
#     plt.legend()
#     plt.show()

# def plot_pivots(df):
#     # df['local_max'] = df['Close'][(df['Close'].shift(1) < df['Close']) & (df['Close'].shift(-1) < df['Close'])]
#     # df['local_min'] = df['Close'][(df['Close'].shift(1) > df['Close']) & (df['Close'].shift(-1) > df['Close'])]

#     colors = plt.rcParams['axes.prop_cycle'].by_key()['color']
#     plt.figure(figsize=(15, 8))
#     plt.plot(df['Close'], zorder=0)
    
#     # plt.scatter(df.index, df['local_max'], s=100, label='Maxima', marker='^', c=colors[1])
#     # plt.scatter(df.index, df['local_min'], s=100, label='Minima', marker='v', c=colors[2])

#     max_idx = argrelextrema(df['Close'].values, np.greater, order=5)[0]
#     min_idx = argrelextrema(df['Close'].values, np.less, order=5)[0]

#     plt.scatter(df.iloc[max_idx].index, df.iloc[max_idx]['Close'],
#                 label='Maxima', s=100, color=colors[1], marker='^')
#     plt.scatter(df.iloc[min_idx].index, df.iloc[min_idx]['Close'],
#                 label='Minima', s=100, color=colors[2], marker='v')
    
#     # Get K consecutive higher peaks
#     K = 2
#     high_idx = argrelextrema(df['Close'].values, np.greater, order=5)[0]
#     highs = df.iloc[high_idx]['Close']
#     extrema = []
#     ex_deque = deque(maxlen=K)
#     for i, idx in enumerate(high_idx):
#         if i == 0:
#             ex_deque.append(idx)
#             continue
#         if highs[i] < highs[i-1]:
#             ex_deque.clear()
#             ex_deque.append(idx)
#         else:
#             ex_deque.append(idx)
#             if len(ex_deque) == K:
#                 # K-consecutive higher highs found
#                 extrema.append(ex_deque.copy())

#     print("Consecutive Higher Highs:", extrema)

#     # Plot consecutive higher highs
#     close = df['Close'].values
#     dates = df.index
#     _ = [plt.plot(dates[list(indices)], close[list(indices)], c=colors[1]) for indices in extrema]

#     plt.xlabel('Time')
#     plt.ylabel('Price')
#     plt.title('Local Maxima and Minima with Consecutive Higher Highs')
#     plt.legend(['Close', 'Maxima', 'Minima', 'Consecutive Higher Highs'])
#     plt.show()


    
    # plt.xlabel('Time')
    # plt.ylabel('Price')
    # plt.title('Local Maxima and Minima')
    # plt.legend()
    # plt.show()
    # plt.close()
import winsound


def plot_pivots(df, tokenid, exchange, current=False, date_input =None, starttime_input=None, endtime_input=None,  plot=False):
    plot=plot
    # print('inside plot piviot')
    # print(f'plot', plot)
    # colors = plt.rcParams['axes.prop_cycle'].by_key()['color']
    # plt.figure(figsize=(15, 8))
    # fig, plt1 = plt.subplots(figsize=(15, 8))
    # plt.figure(figsize=(10, 6), dpi=100)
    # plt.plot(df['Close'], zorder=0)
    

    close = df['Close'].values
    dates = df.index
    sideways = False
    isvander = False
    overall_trend =''
    trend_label = ''
    signal_text = '' 
    isvander = False

    order = 5
    K = 2

    hh = getHigherHighs(close, order, K)
    hl = getHigherLows(close, order, K)
    ll = getLowerLows(close, order, K)
    lh = getLowerHighs(close, order, K)

    # _ = [plt.plot(dates[list(indices)], close[list(indices)], c=colors[1]) for indices in hh]
    # _ = [plt.plot(dates[list(indices)], close[list(indices)], c=colors[2]) for indices in hl]
    # _ = [plt.plot(dates[list(indices)], close[list(indices)], c=colors[3]) for indices in ll]
    # _ = [plt.plot(dates[list(indices)], close[list(indices)], c=colors[4]) for indices in lh]

    # # Add confirmation markers
    # _ = [plt.scatter(dates[indices[-1]], close[indices[-1]], c=colors[1], marker='^', s=100) for indices in hh]
    # _ = [plt.scatter(dates[indices[-1]], close[indices[-1]], c=colors[2], marker='^', s=100) for indices in hl]
    # _ = [plt.scatter(dates[indices[-1]], close[indices[-1]], c=colors[3], marker='v', s=100) for indices in ll]
    # _ = [plt.scatter(dates[indices[-1]], close[indices[-1]], c=colors[4], marker='v', s=100) for indices in lh]

  

    # plt.text(dates[-1], close[-1], trend_label, ha='right', va='top', fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
    # Determine the overall trend
    isvander = False
    isvander, signal_text = check_vander1(tokenid=tokenid, exchange = exchange,  current=current, date_input=date_input, starttime_input = starttime_input, endtime_input = endtime_input)
    # print(f'isvander: {isvander}')
    latest_hh_confirmation = bool(hh and hh[-1][-1] >= order)
    latest_hl_confirmation = bool(hl and hl[-1][-1] >= order)
    latest_ll_confirmation = bool(ll and ll[-1][-1] >= order)
    latest_lh_confirmation = bool(lh and lh[-1][-1] >= order)

    if latest_hh_confirmation and not latest_ll_confirmation:
        overall_trend = 'Bullish'
    elif latest_ll_confirmation and not latest_hh_confirmation:
        overall_trend = 'Bearish'
    elif latest_hl_confirmation and latest_lh_confirmation:
        overall_trend = 'Sideways'
        sideways = True
        # isvander = False
        
          # # Find the overall latest trend
        latest_hh = hh[-1][-1] if hh else None
        latest_hl = hl[-1][-1] if hl else None
        latest_ll = ll[-1][-1] if ll else None
        latest_lh = lh[-1][-1] if lh else None
    
        latest_trend = max(latest_hh, latest_hl, latest_ll, latest_lh, key=lambda x: x if x is not None else float('-inf'))
    
        if latest_trend == latest_hh:
            trend_label = 'Latest Trend: Higher High,'
        elif latest_trend == latest_hl:
            trend_label = 'Latest Trend: Higher Low,'
        elif latest_trend == latest_ll:
            trend_label = 'Latest Trend: Lower Low,'
        else:
            trend_label = 'Latest Trend: Lower High,'
        if (isvander ==  True):
            new0=0
            
            # plt.text(dates[-1], close[-1], f'Overall Trend: {overall_trend}', ha='left', va='bottom', fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
            # plt.text(dates[-1], close[-1], trend_label, ha='right', va='top', fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
            # plt.text(dates[-1], close[-1], trend_label, ha='right', va='top', fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
            # plt.text(0.3, 0.3, f'{trend_label}{overall_trend}', ha='right', va='top', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
            # plt.xlabel('Time')
            # plt.ylabel('Price')
            # plt.title('Potential Divergence Points for Closing Price')
            # legend_elements = [
            # Line2D([0], [0], color=colors[0], label='Close'),
            # Line2D([0], [0], color=colors[1], label='Higher Highs'),
            # Line2D([0], [0], color='w', marker='^', markersize=10, markerfacecolor=colors[1], label='Higher High Confirmation'),
            # Line2D([0], [0], color=colors[2], label='Higher Lows'),
            # Line2D([0], [0], color='w', marker='^', markersize=10, markerfacecolor=colors[2], label='Higher Lows Confirmation'),
            # Line2D([0], [0], color=colors[3], label='Lower Lows'),
            # Line2D([0], [0], color='w', marker='v', markersize=10, markerfacecolor=colors[3], label='Lower Lows Confirmation'),
            # Line2D([0], [0], color=colors[4], label='Lower Highs'),
            # Line2D([0], [0], color='w', marker='v', markersize=10, markerfacecolor=colors[4], label='Lower Highs Confirmation')
            # ]
            # plt.legend(handles=legend_elements)
            # plt.show()
        
    else:
        overall_trend = 'Inconclusive'
    # plt.text(dates[-1], close[-1], f'Overall Trend: {overall_trend}', ha='left', va='bottom', fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))



    # plt.xlabel('Time')
    # plt.ylabel('Price')
    # plt.title('Potential Divergence Points for Closing Price')
    # legend_elements = [
    #     Line2D([0], [0], color=colors[0], label='Close'),
    #     Line2D([0], [0], color=colors[1], label='Higher Highs'),
    #     Line2D([0], [0], color='w', marker='^', markersize=10, markerfacecolor=colors[1], label='Higher High Confirmation'),
    #     Line2D([0], [0], color=colors[2], label='Higher Lows'),
    #     Line2D([0], [0], color='w', marker='^', markersize=10, markerfacecolor=colors[2], label='Higher Lows Confirmation'),
    #     Line2D([0], [0], color=colors[3], label='Lower Lows'),
    #     Line2D([0], [0], color='w', marker='v', markersize=10, markerfacecolor=colors[3], label='Lower Lows Confirmation'),
    #     Line2D([0], [0], color=colors[4], label='Lower Highs'),
    #     Line2D([0], [0], color='w', marker='v', markersize=10, markerfacecolor=colors[4], label='Lower Highs Confirmation')
    # ]
    
    # plt.legend(handles=legend_elements)
    # if(isvander == True):
    #     winsound.PlaySound('SystemAsterisk', winsound.SND_ALIAS)
    #     plt.show()
    # else:
    #     print('Vander not fulfilled')
    # print('checking to plot inside plot piviot')
    if (overall_trend == 'Sideways' and plot == True):
         if(isvander == True and plot == True):
            # print('plotting inside plot piviot function')
            colors = plt.rcParams['axes.prop_cycle'].by_key()['color']
         
            isvander, signal_text = check_vander(tokenid=tokenid, exchange = exchange,  current=current, date_input=date_input, starttime_input = starttime_input, endtime_input = endtime_input)
    
            plt.figure(figsize=(15, 8))
            plt.plot(df['Close'], zorder=0)
            _ = [plt.plot(dates[list(indices)], close[list(indices)], c=colors[1]) for indices in hh]
            _ = [plt.plot(dates[list(indices)], close[list(indices)], c=colors[2]) for indices in hl]
            _ = [plt.plot(dates[list(indices)], close[list(indices)], c=colors[3]) for indices in ll]
            _ = [plt.plot(dates[list(indices)], close[list(indices)], c=colors[4]) for indices in lh]
        
            # Add confirmation markers
            _ = [plt.scatter(dates[indices[-1]], close[indices[-1]], c=colors[1], marker='^', s=100) for indices in hh]
            _ = [plt.scatter(dates[indices[-1]], close[indices[-1]], c=colors[2], marker='^', s=100) for indices in hl]
            _ = [plt.scatter(dates[indices[-1]], close[indices[-1]], c=colors[3], marker='v', s=100) for indices in ll]
            _ = [plt.scatter(dates[indices[-1]], close[indices[-1]], c=colors[4], marker='v', s=100) for indices in lh]

            plt.xlabel('Time')
            plt.ylabel('Price')
            plt.title('Potential Divergence Points for Closing Price')
            legend_elements = [
                Line2D([0], [0], color=colors[0], label='Close'),
                Line2D([0], [0], color=colors[1], label='Higher Highs'),
                Line2D([0], [0], color='w', marker='^', markersize=10, markerfacecolor=colors[1], label='Higher High Confirmation'),
                Line2D([0], [0], color=colors[2], label='Higher Lows'),
                Line2D([0], [0], color='w', marker='^', markersize=10, markerfacecolor=colors[2], label='Higher Lows Confirmation'),
                Line2D([0], [0], color=colors[3], label='Lower Lows'),
                Line2D([0], [0], color='w', marker='v', markersize=10, markerfacecolor=colors[3], label='Lower Lows Confirmation'),
                Line2D([0], [0], color=colors[4], label='Lower Highs'),
                Line2D([0], [0], color='w', marker='v', markersize=10, markerfacecolor=colors[4], label='Lower Highs Confirmation')
                ]
    
            plt.legend(handles=legend_elements)

            
            # winsound.PlaySound('SystemAsterisk', winsound.SND_ALIAS)
            plt.show()
            # plt.close()
         if (overall_trend == 'Sideways'):
           sideways = True
           # print("sideways")
    else: 
        no0=0
        # print(overall_trend)
    # print('completed plot piviot function')
    
    return sideways, overall_trend, trend_label, signal_text, isvander

def getHigherLows(data: np.array, order=5, K=2):
    '''
    Finds consecutive higher lows in price pattern.
    Must not be exceeded within the number of periods indicated by the width
    parameter for the value to be confirmed.
    K determines how many consecutive lows need to be higher.
    '''
    # Get lows
    low_idx = argrelextrema(data, np.less, order=order)[0]
    lows = data[low_idx]
    # Ensure consecutive lows are higher than previous lows
    extrema = []
    ex_deque = deque(maxlen=K)
    for i, idx in enumerate(low_idx):
        if i == 0:
            ex_deque.append(idx)
            continue
        if lows[i] < lows[i-1]:
            ex_deque.clear()

        ex_deque.append(idx)
        if len(ex_deque) == K:
            extrema.append(ex_deque.copy())

    return extrema

def getLowerHighs(data: np.array, order=5, K=2):
    '''
    Finds consecutive lower highs in price pattern.
    Must not be exceeded within the number of periods indicated by the width
    parameter for the value to be confirmed.
    K determines how many consecutive highs need to be lower.
    '''
    # Get highs
    high_idx = argrelextrema(data, np.greater, order=order)[0]
    highs = data[high_idx]
    # Ensure consecutive highs are lower than previous highs
    extrema = []
    ex_deque = deque(maxlen=K)
    for i, idx in enumerate(high_idx):
        if i == 0:
            ex_deque.append(idx)
            continue
        if highs[i] > highs[i-1]:
            ex_deque.clear()

        ex_deque.append(idx)
        if len(ex_deque) == K:
            extrema.append(ex_deque.copy())

    return extrema

def getHigherHighs(data: np.array, order=5, K=2):
    '''
    Finds consecutive higher highs in price pattern.
    Must not be exceeded within the number of periods indicated by the width
    parameter for the value to be confirmed.
    K determines how many consecutive highs need to be higher.
    '''
    # Get highs
    high_idx = argrelextrema(data, np.greater, order=5)[0]
    highs = data[high_idx]
    # Ensure consecutive highs are higher than previous highs
    extrema = []
    ex_deque = deque(maxlen=K)
    for i, idx in enumerate(high_idx):
        if i == 0:
            ex_deque.append(idx)
            continue
        if highs[i] < highs[i-1]:
            ex_deque.clear()

        ex_deque.append(idx)
        if len(ex_deque) == K:
            extrema.append(ex_deque.copy())

    return extrema

def getLowerLows(data: np.array, order=5, K=2):
    '''
    Finds consecutive lower lows in price pattern.
    Must not be exceeded within the number of periods indicated by the width
    parameter for the value to be confirmed.
    K determines how many consecutive lows need to be lower.
    '''
    # Get lows
    low_idx = argrelextrema(data, np.less, order=order)[0]
    lows = data[low_idx]
    # Ensure consecutive lows are lower than previous lows
    extrema = []
    ex_deque = deque(maxlen=K)
    for i, idx in enumerate(low_idx):
        if i == 0:
            ex_deque.append(idx)
            continue
        if lows[i] > lows[i-1]:
            ex_deque.clear()

        ex_deque.append(idx)
        if len(ex_deque) == K:
            extrema.append(ex_deque.copy())

    return extrema

    
def generate_sample_df_with_pattern(pattern, data1, date_input, starttime_input,endtime_input ):
    date_rng = pd.date_range(start='1/1/2020', end='1/10/2020', freq='D')
    data = {'date': date_rng}
    
    # start_date = pd.Timestamp('2024-05-24 12:34:00')
    # end_date = pd.Timestamp('2024-05-24 13:36:00')
    # date_rng = pd.date_range(start=start_date, end=end_date, freq='T')
    start_date = pd.Timestamp(date_input + ' ' + starttime_input)
    end_date = pd.Timestamp(date_input + ' ' + endtime_input)
    date_rng = pd.date_range(start=start_date, end=end_date, freq='min')

    data = {'date': date_rng}

    data = {'date': date_rng}
    if pattern == 'Head and Shoulder':
        data['Open'] = data1['Open'].tolist()
        data['High'] = data1['High'].tolist()
        data['Low'] = data1['Low'].tolist()
        data['Close'] = data1['Close'].tolist()
        data['Volume'] = data1['volume'].tolist()
    elif pattern == 'Inverse Head and Shoulder':
        data['Open'] = data1['Open'].tolist()
        data['High'] = data1['High'].tolist()
        data['Low'] = data1['Low'].tolist()
        data['Close'] = data1['Close'].tolist()
        data['Volume'] = data1['volume'].tolist()
    elif pattern == "Double Top" or "Double Bottom" or "Ascending Triangle" or "Descending Triangle":
        data['High'] = data1['High'].tolist()
        data['Low'] = data1['Low'].tolist()
        data['Close'] = data1['Close'].tolist()
        df = pd.DataFrame(data)
        df.iloc[3:5,1] =100
        df.iloc[6:8,1] =70
        df.iloc[6:9,2] =70
    df = pd.DataFrame(data)
    return df
    
def test_detect_head_shoulder(tokenid, exchange, hours = 1, current=False, date_input=None,starttime_input=None,endtime_input=None, plot = False):
    
    
     # Get the current date and time
    now = datetime.datetime.now()
    sideways = False
    overall_trend =''
    trend_label = ''
    signal_text = '' 
    isvander = False

    if date_input is None:
        # print('dates none')
            # Extract the date, start time, and end time from the current datetime
        date_input = now.date().strftime('%d-%m-%Y')
        endtime_input = now.time().strftime('%H:%M')
        starttime_input = (now - datetime.timedelta(hours=hours)).time().strftime('%H:%M')
    else:
        # print('dates not none')
        date_input = date_input
        endtime_input = endtime_input
        starttime_input = starttime_input
        
    
    # date_input = '27-05-2024'
    # starttime_input = '14:20'
    # endtime_input = '15:29'
            
    # print("date_input:", date_input)
    # print("starttime_input:", starttime_input)
    # print("endtime_input:", endtime_input)
            
            
            
            
            
            
            
            
    start_timestamp, end_timestamp = get_start_end_timestamps(date_input, starttime_input, endtime_input)
    # print(f'starttime_input2', starttime_input)
    # print(f'endtime_input2', endtime_input)
    # print(f'date_input2', date_input)
    # print(f'Start timestamp: {start_timestamp}')
    # print(f'End timestamp: {end_timestamp}')
    #print()
            # Retry the API call up to 3 times if there is an error
    max_retries = 3
    retries = 0
    while retries < max_retries:
                try:
                    # data = api.get_time_price_series(exchange='NSE', token='11872', starttime=start_timestamp, endtime=end_timestamp, interval=1)
                    if exchange == 'NFO' or exchange == 'NSE':
                        # print('fetching data for NFO/NSE')
                        data = api.get_time_price_series(exchange='NSE', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                    elif exchange == 'MCX':
                        data = api.get_time_price_series(exchange='MCX', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                    break  # If the API call is successful, exit the loop
                except Exception as e:
                    print(f"Error: {e}")
                    retries += 1
                    if retries == max_retries:
                        print("Maximum number of retries reached. Unable to fetch data.")
                    else:
                        print(f"Retrying in 2 seconds... (Attempt {retries}/{max_retries})")
                        time.sleep(1)
                        #ver5
            
    if retries < max_retries:
                new0=0
            
    data = live_data(data)
    data = data.sort_values(by='time')
    data1=data
    # print(data)
            
    data = data['Close'].values
    # print(data1['Open'].values)

                
    # Generate data with head and shoulder pattern
    pattern = "Double Top"
    # df_head_shoulder = generate_sample_df_with_pattern("Head and Shoulder")
    # df_inv_shoulder = generate_sample_df_with_pattern("Inverse Head and Shoulder")
    df_pivot_1 = generate_sample_df_with_pattern(pattern, data1, date_input, starttime_input, endtime_input )

     # Set the 'date' column as the index
    df_pivot_1 = df_pivot_1.set_index('date')


    
    # df_with_detection = tp1.detect_head_shoulder(df_head_shoulder)
    # df_with_inv_detection = tp1.detect_head_shoulder(df_inv_shoulder)
    df_pivot = find_pivots(df_pivot_1, 2)
    
    # print(df_with_inv_detection)
    # print(df_head_shoulder)
    pd.set_option('display.max_rows', None)
    pd.set_option('display.max_columns', None)
    # print(df_pivot)
    sideways, overall_trend, trend_label, signal_text,isvander=plot_pivots(df_pivot, tokenid, exchange, current, date_input, starttime_input, endtime_input, plot=plot)
    return sideways, overall_trend, trend_label, signal_text, isvander
    
    # assert "Head and Shoulder" in df_with_detection['head_shoulder_pattern'].values
    # assert "Inverse Head and Shoulder" in df_with_inv_detection['head_shoulder_pattern'].values





#Test Nadarya Watson (check_vander) signal function to see if there is signal in last 3 minutes of selected script and time for MCX and NFO
exchange1 = 'NFO'
date_input = '20-06-2025'
endtime_input = '12:15'
starttime_input = '10:00'


if (exchange1 == 'NFO'):
    search_text_2 = 'AARTIIND31JUL25C470'
    search_text = search_text_2
    first_digit_index1 = next((i for i, char in enumerate(search_text_2) if char.isdigit()), None)
elif (exchange1 == 'MCX'):
    search_text_2 = 'CRUDEOIL17JUL25C5400'
    search_text = search_text_2
    first_digit_index1 = next((i for i, char in enumerate(search_text_2) if char.isdigit()), None)
            
           
            
# If a digit is found, extract the substring before the first digit
if first_digit_index1 is not None:
    new_string = search_text_2[:first_digit_index1]
    if exchange1 == 'NFO':
        ret2 = api.searchscrip(exchange='NSE', searchtext=new_string+'-EQ')
    elif exchange1 == 'MCX':
            if new_string == 'CRUDEOIL':
                new_string = 'CRUDEOIL17JUL25C5900'
            ret2 = api.searchscrip(exchange='MCX', searchtext=new_string)
                # print(token)
    print(new_string)
    print(ret2)
    tokenid = ret2['values'][0]['token']
    print(tokenid)
    # issideways = test_detect_head_shoulder(tokenid=tokenid, exchange1)
    # if (issideways):
    #     check_vander(tokenid)
else:
    new_string = search_text
isvander, signal_text = check_vander(tokenid=tokenid, exchange = exchange1, current=False, date_input =date_input, starttime_input=starttime_input, endtime_input=endtime_input)
print(signal_text)
print(isvander)

# tesing checking higher highs, higher lows, etc to detect the sideways trend during Nadarya Watson (check_vander) signal  to see if there is signal in last 3 minutes of selected script and time for MCX and NFO and the stock is trending sideways and what is overall trend
try:
    sideways, overall_trend, trend_label, signal_text,isvander =  issideways2, overall_trend2, trend_label2, signal_text2, isvander2 = test_detect_head_shoulder(tokenid=api.searchscrip(exchange='NSE', searchtext='AARTIIND')['values'][0]['token'],exchange='NFO',hours=1.2,current=False,date_input=date_input,starttime_input=starttime_input,endtime_input=endtime_input, plot=True)
    time.sleep(0.5)
    print(overall_trend)
    print(isvander)
    if (sideways==True):
        print("its sideways")
        print(sideways)
        print(overall_trend)
        # print(trend_label)
        # print(signal_text)
except Exception as e:
        print(f"Error occurred for {search_text}: {e}")
     # issideways1, overall_trend1, trend_label1, signal_text1, isvander = test_sideways_and_nadarya_envelope(tokenid=tokenid,exchange=exchange,hours=0.7,current=False,
     #                                                                                                       date_input=date,starttime_input=start_time,endtime_input=end_time)
        

#backtester ver4 using nadaraya envelope when the stock is sideways for entry (using check_sideways_and_nadarya), and custom stop loss/book profit using mutiple methods of generting exit signals like technicals, custom stop loss logic, book profit, etc for exit and also calcution and saving of technicals during entry and exit for further study. 
from datetime import datetime
from datetime import timedelta
import numpy as np
import warnings
import io
from docx import Document
from contextlib import redirect_stdout
import warnings
import pandas as pd
import traceback
import linecache
warnings.filterwarnings("ignore", category=RuntimeWarning)
warnings.filterwarnings("ignore")

last_stock_traded=''
last_stock_traded_time=''

def test_sideways_and_nadarya_envelope(tokenid, exchange, hours, date_input, starttime_input, endtime_input, current = False ):
        import random
        trends = ['Latest Trend: Higher High', 'Latest Trend: Higher Low', 
                  'Latest Trend: Lower Low', 'Latest Trend: Lower High']
        return True, 'Uptrend', random.choice(trends), 'Buy', True


def check_sideways_and_nadarya(ticker, tokenid, exchange, start, end, date):
    import datetime
    from datetime import timedelta
    start_time = datetime.datetime.strptime(start, '%H:%M')
    end_time = datetime.datetime.strptime(end, '%H:%M')
    time_diff = (end_time - start_time).total_seconds() / 3600
    if time_diff < 0.7:
        start_time = end_time - timedelta(hours=0.7)
        market_open = datetime.datetime.strptime('09:30', '%H:%M')
        if start_time < market_open:
            start_time = market_open
        start_time = start_time.strftime('%H:%M')
        end_time = end_time.strftime('%H:%M')
    else:
        start_time = start
        end_time = end
    # print(f'start_time1 ', start_time)
    # print(f'end_time1', end_time)
    issideways1, overall_trend1, trend_label1, signal_text1, isvander = test_detect_head_shoulder(tokenid=tokenid,exchange=exchange,hours=0.7,current=False,
                                                                                                           date_input=date,starttime_input=start_time,endtime_input=end_time, plot=False)
    # print(f'issideways1', issideways1)
    # print(f'overall_trend1', overall_trend1)
    # print(f'trend_label1', trend_label1)
    # print(f'signal_text1', signal_text1)
    # print(f'isvander', isvander)
   
    
    if issideways1 and isvander:
        # print(f"stock: {ticker}")
        # print(f'overall trend in 0.7 is {overall_trend1} with latest trend: {trend_label1} and latest vander signal: {signal_text1}')
        start_time = datetime.datetime.strptime(start, '%H:%M')
        end_time = datetime.datetime.strptime(end, '%H:%M')
        time_diff = (end_time - start_time).total_seconds() / 3600  # Convert to hours
        if time_diff < 1.2:
            start_time = end_time - timedelta(hours=0.7)
            market_open = datetime.datetime.strptime('09:30', '%H:%M')
            if start_time < market_open:
                start_time = market_open
            start_time = start_time.strftime('%H:%M')
            end_time = end_time.strftime('%H:%M')
        else:
            start_time = start
            end_time = end
        issideways2, overall_trend2, trend_label2, signal_text2, isvander2 = test_detect_head_shoulder(tokenid=tokenid,exchange=exchange,hours=1.2,current=False,date_input=date,starttime_input=start_time,endtime_input=end_time, plot=False) 
        # print(f'overall trend in 1.2 is {overall_trend2} with latest trend: {trend_label2} and latest vander signal: {signal_text2}')
        if issideways2 and isvander2:
            if  signal_text2 in ['Last 5 mins: Lower band signal present']:
                # print('call buy at start')
                signal = 1  # Signal to buy call
            elif signal_text2 in ['Last 5 mins: Upper band signal present']:
                # print('put buy at start')
                signal = -1  # Signal to buy put
            else:
                # print('no signal at start')
                signal = 0
                
            # if trend_label2 in ['Latest Trend: Higher High,', 'Latest Trend: Higher Low,'] and signal_text2 in ['Last -3 mins: Lower band signal present']:
            #     print('call buy at start')
            #     signal = 1  # Signal to buy call
            # elif trend_label2 in ['Latest Trend: Lower Low,', 'Latest Trend: Lower High,'] and signal_text2 in ['Last -3 mins: Upper band signal present']:
            #     print('put buy at start')
            #     signal = -1  # Signal to buy put
            # else:
            #     print('no signal at start')
            #     signal = 0
            print(f'signal', signal)
            print(f"check {['put', 'nothing', 'call'][signal+1]}")
            return True, signal
    # print(f"no trade: stock: {ticker}")
    return False, 0
warnings.filterwarnings("ignore", category=pd.errors.SettingWithCopyWarning)
class StarterSystem:
  def __init__(self, ticker, exchange, start:str, end:str ,date:str, tokenid, target_risk=0.2, stop_loss_gap=0.3,
                 starting_capital=100000, option_cost=0.01, interest_on_balance=0.0,
                  interval=1):
        self.ticker = ticker
        self.exchange = exchange
        self.tokenid = tokenid
        self.interval = interval
        self.target_risk = target_risk
        self.stop_loss_gap = stop_loss_gap
        self.starting_capital = starting_capital
        self.option_cost = option_cost
        self.start = start
        self.end = end
        self.date = date
        self.interest_on_balance = interest_on_balance
        self.daily_iob = (1 + self.interest_on_balance) ** (1 / 252)
        self.signal_names = []
        self._getData()
        self._calcSignals()
        self.api = api
        global last_stock_traded
        global last_stock_traded_time
  def get_quotes(self, exch, token):
        return api.get_quotes(exchange=exch, token=token)
  def search_scrip(self, exchange, searchtext):
        return api.searchscrip(exchange=exchange, searchtext=searchtext)
  def is_otm(self, option_type, strike_price, current_price):
        if option_type == 'CE':
            x=1
            # print(f'is call otm', strike_price > current_price)
            return strike_price > current_price
        elif option_type == 'PE':
            x=1
            # print(f'is put otm', strike_price < current_price)
            return strike_price < current_price
        return False
  def check_price_difference(self, sp1, bp1):
      try:
        if bp1 == 0:
            x=1
            # print(f"Warning: BP1 is zero. SP1={sp1}, BP1={bp1}")
            return False
        print((sp1 - bp1) / bp1)
        return (sp1 - bp1) / bp1 < 0.03
      except ZeroDivisionError:
        x=1
        # print(f"ZeroDivisionError in check_price_difference: SP1={sp1}, BP1={bp1}")
        return False

  def calculate_technicals_at_times(self, entry_time_str, exit_time_str):
    """Calculates technical indicators at specified entry and exit times.

    Args:
        candle_data: A list of dictionaries representing candle data.
        entry_time_str: String representing the entry time ('YYYY-MM-DD HH:MM:SS').
        exit_time_str: String representing the exit time ('YYYY-MM-DD HH:MM:SS').

    Returns:
        A dictionary with technical indicators or None if an error occurs.
    """
    try:
        import talib

        # Extract time strings (HH:MM)
        print(f'entry_time_str',entry_time_str)
        print(f'exit_time_str', exit_time_str)
        # entry_time_only = datetime.datetime.strptime(entry_time_str, '%d-%m-%Y %H:%M:%S').strftime('%H:%M')
        # exit_time_only = datetime.datetime.strptime(exit_time_str, '%d-%m-%Y %H:%M:%S').strftime('%H:%M')
        entry_time_only = '11:30'
        exit_time_only = '15:29'
        print(f'entry_time_only', entry_time_only )
        print(f'exit_time_only', exit_time_only)
        
        start_timestamp, end_timestamp = get_start_end_timestamps(self.date, entry_time_only, exit_time_only)

        candle_data = api.get_time_price_series(exchange='NSE', token=self.tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)

        if candle_data is None or not candle_data:
            print("No candle data received from API.")
            return None

        # Reverse the candle data to chronological order
        candle_data.reverse()

        df = pd.DataFrame(candle_data)
        # print(df)

        # Convert columns to correct types, handling errors
        for col in ['into', 'inth', 'intl', 'intc', 'v']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        df['time'] = pd.to_datetime(df['time'], format='%d-%m-%Y %H:%M:%S')
        df = df.set_index('time')

        entry_time = pd.to_datetime(entry_time_str)
        exit_time = pd.to_datetime(exit_time_str)

        # Calculate indicators (using 'v' for volume)
        df['SMA_10'] = talib.SMA(df['intc'], timeperiod=10)
        df['EMA_20'] = talib.EMA(df['intc'], timeperiod=20)
        df['RSI'] = talib.RSI(df['intc'], timeperiod=14)
        df['ADX'] = talib.ADX(df['inth'], df['intl'], df['intc'], timeperiod=14)
        df['MACD'], df['MACD_signal'], df['MACD_hist'] = talib.MACD(df['intc'], fastperiod=12, slowperiod=26, signalperiod=9)
        df['ATR'] = talib.ATR(df['inth'], df['intl'], df['intc'], timeperiod=14)
        df['BB_UPPER'], df['BB_MIDDLE'], df['BB_LOWER'] = talib.BBANDS(df['intc'], timeperiod=20)
        df['OBV'] = talib.OBV(df['intc'], df['v'])

        # Extract indicator values
        entry_tech = df.loc[entry_time]
        exit_tech = df.loc[exit_time]

        results = {
            'entry_time': entry_time,
            'exit_time': exit_time,
            'entry_SMA_10': entry_tech['SMA_10'],
            'entry_EMA_20': entry_tech['EMA_20'],
            'entry_RSI': entry_tech['RSI'],
            'entry_ADX': entry_tech['ADX'],
            'entry_MACD': entry_tech['MACD'],
            'entry_MACD_signal': entry_tech['MACD_signal'],
            'entry_MACD_hist': entry_tech['MACD_hist'],
            'entry_ATR': entry_tech['ATR'],
            'entry_BB_UPPER': entry_tech['BB_UPPER'],
            'entry_BB_MIDDLE': entry_tech['BB_MIDDLE'],
            'entry_BB_LOWER': entry_tech['BB_LOWER'],
            'entry_OBV': entry_tech['OBV'],
            'exit_SMA_10': exit_tech['SMA_10'],
            'exit_EMA_20': exit_tech['EMA_20'],
            'exit_RSI': exit_tech['RSI'],
            'exit_ADX': exit_tech['ADX'],
            'exit_MACD': exit_tech['MACD'],
            'exit_MACD_signal': exit_tech['MACD_signal'],
            'exit_MACD_hist': exit_tech['MACD_hist'],
            'exit_ATR': exit_tech['ATR'],
            'exit_BB_UPPER': exit_tech['BB_UPPER'],
            'exit_BB_MIDDLE': exit_tech['BB_MIDDLE'],
            'exit_BB_LOWER': exit_tech['BB_LOWER'],
            'exit_OBV': exit_tech['OBV'],
        }
        return results

    except KeyError as e:
        print(f"Error: Data not found for specified time: {e}")
        return None
    except Exception as e:
        print(f"An error occurred during technical indicator calculation: {e}")
        return None
  def check_lot_price(self, sp1, lot_size):
        
        total_price = sp1 * lot_size
        # print(f'total_price', total_price)
        return 4000 < total_price < 25000
  def get_start_end_timestamps(self, date_input, starttime_input, endtime_input):
    import datetime as datetime
    date_parts = date_input.split('-')
    year, month, day = int(date_parts[2]), int(date_parts[1]), int(date_parts[0])
    start_time = datetime.datetime.strptime(starttime_input, '%H:%M').time()
    end_time = datetime.datetime.strptime(endtime_input, '%H:%M').time()
    start_datetime = datetime.datetime(year, month, day, start_time.hour, start_time.minute)
    end_datetime = datetime.datetime(year, month, day, end_time.hour, end_time.minute)
    start_timestamp = start_datetime.timestamp()
    end_timestamp = end_datetime.timestamp()
    return start_timestamp, end_timestamp
  def get_option_data(self, signal):
    try:
        # print('inside get option data')
        import datetime
        df = None
        now = datetime.datetime.now()
        hours = 1
        date_input = now.date().strftime('%d-%m-%Y')
        endtime_input = now.time().strftime('%H:%M')
        starttime_input = (now - timedelta(hours=hours)).time().strftime('%H:%M')
        date_input = self.date
        starttime_input = self.start
        endtime_input = self.end      
        start_timestamp, end_timestamp = self.get_start_end_timestamps(date_input, starttime_input, endtime_input)
        # print(f'start_timestamp', starttime_input)
        # print(f'end_timestamp',endtime_input)
        from datetime import datetime
        # time_str = selected_option['time'].split()[1]  # Get the time part
            
        #     # Create a datetime object with the extracted time
        # time_obj_1 = datetime.strptime(starttime_input, '%H:%M').time()
            
        #     # Check if the time is greater than 15:00:00
        # if time_obj_1 > datetime.strptime('15:00', '%H:%M').time():
        #         print("Time is greater than 15:00:00")
        #         return None, None
            
        strikeprice = round(self.current_price / 100) * 100
        self.data['option_underlying_price']=strikeprice
        symbol_expiry = '26DEC24'  # You might want to make this dynamic
        future_symbol = f"{self.ticker}{symbol_expiry}F"
        count = 10
        ret3 = api.get_option_chain('NFO', future_symbol, strikeprice, count)
        # print(f'strikeprice', strikeprice)
        # print(f'ret3', ret3)
        option_chain = ret3['values']
        self.data['lot_size'] = ret3['values'][0]['ls']
        # print(f'old option chain', option_chain)
        option_type = 'CE' if signal == 1 else 'PE'
        option_chain = [opt for opt in option_chain if opt['optt'] == option_type]
        # print(f'new option chain', option_chain)

        # Sort based on signal
        if signal == 1:  # For calls, find strikes above spot price
            option_chain = [opt for opt in option_chain if float(opt['strprc']) > float(strikeprice)]
            option_chain.sort(key=lambda x: float(x['strprc']))  # Sort by strike price ascending
           
        else:  # For puts, find strikes below spot price
            option_chain = [opt for opt in option_chain if float(opt['strprc']) < float(strikeprice)]
            option_chain.sort(key=lambda x: float(x['strprc']), reverse=True)  # Sort by strike price descending
          

        #    # Sort based on signal
        # if signal == 1:  # For calls, find strikes above spot price
        #     option_chain = [opt for opt in option_chain if float(opt['strprc']) > float(strikeprice)]
        #     option_chain.sort(key=lambda x: float(x['strprc']))  # Sort by strike price ascending
           
        # else:  # For puts, find strikes below spot price
        #     option_chain = [opt for opt in option_chain if float(opt['strprc']) < float(spot_price)]
        #     option_chain.sort(key=lambda x: float(x['strprc']), reverse=True)  # Sort by strikeprice price descending
          

        # print(f'new option chain OTM', option_chain)
        max_retries=3


        valid_ce_options = []
        valid_pe_options = []
        current_price=''
        current_time=''
        current_volume=''
        for option in option_chain:
                retries = 0
                while retries < max_retries:
                    try:
                        option_quotes = self.get_quotes('NFO', option['token'])
                        # print('option_quotes:')
                        # print(option_quotes)
                        if option_quotes['stat'] != 'Ok':
                            raise Exception("Quote status not Ok")
                        option_type = option['optt']
                        strike_price = float(option['strprc'])
                        sp1 = float(option_quotes.get('sp1', 0))
                        bp1 = float(option_quotes.get('bp1', 0))
                        lot_size = int(option['ls'])
                        tokenid = option['token']
                        df = api.get_time_price_series(exchange='NFO', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                        # print(df)
                        if isinstance(df, list) and len(df) > 0:
                            #print('inside instance')
                            # print(df)
                            latest_candle = df[0]
                            if isinstance(latest_candle, dict) and 'intc' in latest_candle and 'intv' in latest_candle:
                                current_price = float(latest_candle['intc'])
                                current_volume = int(latest_candle['intv'])
                                current_time = latest_candle['time']
                                # print(f"Current price set to: {current_price}")
                                # print(f"Current volume set to: {current_volume}")
                                # print(f"Current time set to: {current_time}")
                            else:
                                raise Exception("Unable to extract 'intc' or 'intv' from the latest candle")
                        else:
                            raise Exception("Invalid or empty response from get_time_price_series")
    
                        # is_otm = self.is_otm(option_type, strike_price, current_price)
                        lot_price_check = self.check_lot_price(current_price, lot_size)
                        # print(f'lot_price_check', lot_price_check)
    
                        if lot_price_check:
                            option_data = {
                                'symbol': option['tsym'],
                                'type': option_type,
                                'strike': strike_price,
                                'token': option['token'],
                                'lot_size': lot_size,
                                'volume': current_volume,
                                'price': current_price,
                                'time': current_time
                            }
                            if option_type == 'CE':
                                valid_ce_options.append(option_data)
                            elif option_type == 'PE':
                                valid_pe_options.append(option_data)
                        break
                    except Exception as e:
                        retries += 1
                        if retries == max_retries:
                            x=1
                            # print(f"Maximum number of retries reached for option {option['token']}. Error: {str(e)}")
                        else:
                            # print(f"Error occurred: {str(e)}. Retrying in 1 second... (Attempt {retries}/{max_retries})")
                            time.sleep(1)

        if signal > 0:  # Call option
                selected_options = valid_ce_options
        elif signal < 0:  # Put option
                selected_options = valid_pe_options
        else:
                x=1
                # print("No trade signal (signal is 0)")
                return None, None  # No trade signal
        if selected_options:
            
            selected_option = max(selected_options, key=lambda x: x['volume'])
            # print(selected_option)
            # print(f'selected_option', selected_option)
            # Extract the time string
            # from datetime import datetime
            # time_str = selected_option['time'].split()[1]  # Get the time part
            
            # # Create a datetime object with the extracted time
            # time_obj = datetime.strptime(time_str, '%H:%M:%S').time()
            
            # # Check if the time is greater than 15:00:00
            # if time_obj > datetime.strptime('15:00:00', '%H:%M:%S').time():
            #     print("Time is greater than 15:00:00")
            #     return None, None
            
            option_result = {
                'CE' if signal > 0 else 'PE': {
                    'token': selected_option['token'],
                    'symbol': selected_option['symbol']
                }
            }
            # print(f'option_result', option_result)
            # print(f'selected_option', selected_option)
            return option_result, selected_option
        else:
            return None, None  # Return None, None if no valid options found
    except Exception as e:
            print(f"Error in get_option_data: {str(e)}")
            return None, None  # Return None, None in case of any exception
  def live_data(self, data):
        import pandas as pd
        from datetime import datetime
        response = data
        time = []
        open_ = []
        high_ = []
        low_ = []
        close_ = []
        volume = []
        for candle in response:
            time.append(datetime.strptime(candle['time'], '%d-%m-%Y %H:%M:%S'))
            open_.append(float(candle['into']))
            high_.append(float(candle['inth']))
            low_.append(float(candle['intl']))
            close_.append(float(candle['intc']))
            volume.append(float(candle['intv']))
        if close_:
            self.current_price = close_[-1]  # Assuming 'self' is the class instance
    
        candles = pd.DataFrame({
            "Open": open_,
            "High": high_,
            "Low": low_,
            "Close": close_,
            "volume": volume,
            "time": time
        })
        candles = candles.set_index("time")
        return candles

  def set_current_price(self):
    import datetime
    df = None
    now = datetime.datetime.now()
    hours = 1
    date_input = now.date().strftime('%d-%m-%Y')
    endtime_input = now.time().strftime('%H:%M')
    starttime_input = (now - timedelta(hours=hours)).time().strftime('%H:%M')
    date_input = self.date
    starttime_input = self.start
    endtime_input = self.end
    start_timestamp, end_timestamp = self.get_start_end_timestamps(date_input, starttime_input, endtime_input)
    print(f'start_timestamp', self.start)
    print(f'end_timestamp', self.end)
    first_digit_index = next((i for i, char in enumerate(self.ticker) if char.isdigit()), None)
    if self.exchange is not None:
        if self.exchange == 'NSE':
            ret2 = api.searchscrip(exchange='NSE', searchtext=self.ticker+'-EQ')
        elif exchange == 'MCX':
            if new_string == 'NATURALGAS':
                    new_string = 'NATURALGAS25JUN24'
            elif new_string == 'CRUDEOIL':
                    new_string = 'CRUDEOIL18JUN24'
            ret2 = api.searchscrip(exchange='MCX', searchtext=new_string)
        tokenid = ret2['values'][0]['token']
    max_retries = 3
    retries = 0
    while retries < max_retries:
                try:
                     if self.exchange == 'NSE':
                        df = api.get_time_price_series(exchange='NSE', token=self.tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                        if isinstance(df, list) and len(df) > 0:
                            latest_candle = df[0]  # Get the first item in the list
                            if isinstance(latest_candle, dict) and 'intc' in latest_candle:
                                current_p = float(latest_candle['intc'])
                                self.current_price = float(latest_candle['intc'])
                            else:
                                current_p = None
                                self.current_price = None
                        else:
                            current_p = None
                            self.current_price = None
                     elif exchange == 'MCX':
                        df = api.get_time_price_series(exchange='MCX', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=interval)
                     break 
                except Exception as e:
                    # print(f"Error: {e}")
                    retries += 1
                    if retries == max_retries:
                        x=1
                        # print("Maximum number of retries reached. Unable to fetch data.")
                    else:
                        # print(f"Retrying in 2 seconds... (Attempt {retries}/{max_retries})")
                        time.sleep(1)

    return current_p
  def _getData(self):
    import datetime
    df = None
    now = datetime.datetime.now()
    hours = 1
    # date_input = now.date().strftime('%d-%m-%Y')
    # endtime_input = now.time().strftime('%H:%M')
    # starttime_input = (now - timedelta(hours=hours)).time().strftime('%H:%M')
    date_input = self.date
    starttime_input = self.start
    endtime_input = self.end
    start_timestamp, end_timestamp = self.get_start_end_timestamps(date_input, starttime_input, endtime_input)
    first_digit_index = next((i for i, char in enumerate(self.ticker) if char.isdigit()), None)
    if self.exchange is not None:
        if self.exchange == 'NSE':
            ret2 = api.searchscrip(exchange='NSE', searchtext=self.ticker+'-EQ')
        elif exchange == 'MCX':
            if new_string == 'NATURALGAS':
                    new_string = 'NATURALGAS25JUN24'
            elif new_string == 'CRUDEOIL':
                    new_string = 'CRUDEOIL18JUN24'
            ret2 = api.searchscrip(exchange='MCX', searchtext=new_string)
        tokenid = ret2['values'][0]['token']
    max_retries = 3
    retries = 0
    while retries < max_retries:
                try:
                    if self.exchange == 'NSE':
                        df = api.get_time_price_series(exchange='NSE', token=self.tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                        # print(f'df', df)
                        if isinstance(df, list) and len(df) > 0:
                            latest_candle = df[0]  # Get the first item in the list
                            if isinstance(latest_candle, dict) and 'intc' in latest_candle:
                                self.current_price = float(latest_candle['intc'])
                            else:
                                self.current_price = None
                        else:
                            # print("Invalid or empty response")
                            self.current_price = None
                    elif exchange == 'MCX':
                        df = api.get_time_price_series(exchange='MCX', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=interval)
                    break  # If the API call is successful, exit the loop
                except Exception as e:
                    print(f"Error: {e}")
                    retries += 1
                    if retries == max_retries:
                        x=1
                        # print("Maximum number of retries reached. Unable to fetch data.")
                    else:
                        # print(f"Retrying in 2 seconds... (Attempt {retries}/{max_retries})")
                        time.sleep(1)
            
    if retries < max_retries:
                new0=0
    df = self.live_data(df)
    self.data = df
  def _calcSignals(self):
        self._calcSidewaysNadaryaSignals()

  def _calcSidewaysNadaryaSignals(self):
        # print('inside _calcSidewaysNadaryaSignals')
        name = 'SidewaysNadarya'
        import datetime
        start_time = datetime.datetime.strptime(self.start, '%H:%M')
        end_time = datetime.datetime.strptime(self.end, '%H:%M')
        time_diff = (end_time - start_time).total_seconds() / 3600  # Convert to hours
        if time_diff < 0.7:
            start_time = end_time - timedelta(hours=0.7)
            market_open = datetime.datetime.strptime('09:30', '%H:%M')
            if start_time < market_open:
                start_time = market_open
            start_time = start_time.strftime('%H:%M')
            end_time=end_time.strftime('%H:%M')
        else:
            start_time=self.start
            end_time=self.end
        issideways1, overall_trend1, trend_label1, signal_text1, isvander = test_detect_head_shoulder(tokenid = self.tokenid,
                                                                                                               exchange = self.exchange,
                                                                                                               hours=0.7, current = False, 
                                                                                                                    date_input=self.date, 
                                                                                                                    starttime_input=start_time,
                                                                                                                    endtime_input=end_time, plot=False)
        if issideways1 and isvander:
            # print(f"stock: {self.ticker}")
            # print(f'overall trend in 0.7 is {overall_trend1} with latest trend: {trend_label1} and latest vander signal: {signal_text1}')
            start_time = datetime.datetime.strptime(self.start, '%H:%M')
            end_time = datetime.datetime.strptime(self.end, '%H:%M')
            time_diff = (end_time - start_time).total_seconds() / 3600  # Convert to hours
            if time_diff < 1.2:
                start_time = end_time - timedelta(hours=0.7)
                market_open = datetime.datetime.strptime('09:30', '%H:%M')
                if start_time < market_open:
                    start_time = market_open
                start_time = start_time.strftime('%H:%M')
                end_time=end_time.strftime('%H:%M')
            else:
                start_time=self.start
                end_time=self.end
                # print('start_time:')
                # print(start_time)
                # print('end_time:')
                # print(end_time)
            issideways2, overall_trend2, trend_label2, signal_text2, isvander2 = test_detect_head_shoulder(tokenid = self.tokenid,
                                                                                                                    exchange=self.exchange,
                                                                                                                    hours=1.2, current = False, 
                                                                                                                    date_input=self.date, 
                                                                                                                    starttime_input=start_time,
                                                                                                                    endtime_input=end_time,  plot=False)
            # print(f'overall trend in 1.2 is {overall_trend2} with latest trend: {trend_label2} and latest vander signal: {signal_text2}')
            # print('calc results inside starter')
            # print(issideways2)
            # print(overall_trend2)
            # print(trend_label2)
            # print(signal_text2)
            # print(isvander2)
            True
            if issideways2 and isvander2:
                # if trend_label2 in ['Latest Trend: Higher High,', 'Latest Trend: Higher Low,']:
                #     print('call buy signal in calc nadarya')
                #     self.data[name] = 1  # Signal to buy call
                # elif trend_label2 in ['Latest Trend: Lower Low,', 'Latest Trend: Lower High,']:
                #     print('sell/putbuy signal in calc nadarya')
                #     self.data[name] = -1  # Signal to buy put
                # else:
                #     print('no signal in calc nadarya')
                #     self.data[name] = 0
                if signal_text2 in ['Last 5 mins: Lower band signal present']:
                    # print('call buy signal in calc nadarya')
                    self.data[name] = 1  # Signal to buy call
                elif signal_text2 in ['Last 5 mins: Upper band signal present']:
                    # print('sell/putbuy signal in calc nadarya')
                    self.data[name] = -1  # Signal to buy put
                else:
                    # print('no signal in calc nadarya')
                    self.data[name] = 0
                
                # print(f"check {['put', 'nothing', 'call'][self.data[name].iloc[-1]+1]}")
            else:
                self.data[name] = 0
        else:
            self.data[name] = 0
            print(f"no trade: stock: {self.ticker}")
        
        self.signal_names.append(name)
        # print(f'signal_names', self.signal_names)
        # print('done calc nadarya')
  def _getSignal(self):
        return self.data['SidewaysNadarya'].iloc[-1]
  def _calcStopPrice(self, price, std, position, signal):
        if position != 0:
            return price * (1 - std * self.stop_loss_gap * np.sign(position))
        else:
            return price * (1 - std * self.stop_loss_gap * np.sign(signal))
  def _sizePosition(self, capital, price):
    contracts = np.floor(self.target_risk * capital / option_price)
    return contract
  def _calcCash(self, cash_balance):
        return cash_balance * self.daily_iob
  # def stop_loss_strategy(self, entry_price, option_entry_time, option_type, option_tokenid):
  #   from datetime import datetime, timedelta
      

  #   current_mid = entry_price
  #   mid_count = 0
  #   option_entry_time = datetime.strptime(option_entry_time, '%d-%m-%Y %H:%M:%S')
      
  #   last_checked_time = option_entry_time
  #   last_checked_time = last_checked_time.timestamp()
  #   print(f'option_entry_time: {option_entry_time}')

  #   # Set start time as entry time and end time as 1 hour after entry time
  #   start_timestamp = option_entry_time
  #   end_timestamp = option_entry_time + timedelta(hours=1)
  #   start_timestamp = start_timestamp.timestamp()
  #   end_timestamp = end_timestamp.timestamp()

  #   # Fetch initial data
  #   df = api.get_time_price_series(exchange='NSE', token=self.tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
  #   print(f'dfindex ', df )
    
  #   while True:
  #       # Filter data after the last checked time
  #       # new_candles = df[datetime.strptime(df.index, '%d-%m-%Y %H:%M:%S') > last_checked_time]
  #       new_candles = [row for row in df if datetime.strptime(str(row['time']), '%d-%m-%Y %H:%M:%S').timestamp() > last_checked_time]
  #       new_candles = pd.DataFrame(new_candles)
  #       print(f'new_candles: {new_candles}')
  #       # new_candles.set_index('time', inplace=True)
        
  #       # if new_candles:
  #       #     # No new data, check if we've reached the end time
  #       #     if datetime.now() >= end_timestamp:
  #       #         return None, None, "Strategy time limit reached"
            
  #       #     time.sleep(60)  # Wait for 1 minute before checking again
  #       #     # Fetch new data
  #       #     df = api.get_time_price_series(exchange='NSE', token=self.tokenid, starttime=last_checked_time, endtime=end_timestamp, interval=1)
            
  #       #     continue

  #       if new_candles.empty:
  #           # No new data, check if we've reached the end time
  #           if datetime.now() >= end_timestamp:
  #               return None, None, "Strategy time limit reached"
            
  #           time.sleep(60)  # Wait for 1 minute before checking again
  #           # Fetch new data
  #           df = api.get_time_price_series(exchange='NSE', token=self.tokenid, starttime=last_checked_time, endtime=end_timestamp, interval=1)
  #           continue
  #       for candle_time, candle in new_candles.iterrows():
  #           # candle_time=candle['time']
  #           candle_time =datetime.strptime(candle['time'], '%d-%m-%Y %H:%M:%S')
  #           print(f'candle_time: {candle_time}')
  #           print(f'candle: {candle}')
  #           last_checked_time = candle_time

  #           # # Ensure necessary columns are present
  #           # if 'Low' not in candle or 'High' not in candle:
  #           #     print(f"Error: Missing 'Low' or 'High' value for candle at {candle_time}")
  #           #     continue

  #           # Check initial stop loss condition
  #           if option_type == 'call':
  #               stop_loss_hit = float(candle['intl']) < entry_price * 0.98
  #           else:  # put
  #               stop_loss_hit = float(candle['inth']) > entry_price * 1.02

  #           if stop_loss_hit:
  #               exit_reason = "Initial stop loss hit"
  #               break

  #           # Start detecting mids only after 5 minutes have passed
  #           if candle_time >= option_entry_time + timedelta(minutes=5):
  #               if option_type == 'call':
  #                   new_mid_condition =float(candle['intl']) > current_mid
  #                   stop_loss_condition = float(candle['inth']) < current_mid * 0.98
  #               else:  # put
  #                   new_mid_condition = float(candle['inth']) < current_mid
  #                   stop_loss_condition = float(candle['inth']) > current_mid * 1.02

  #               if new_mid_condition:
  #                   # Update mid to the middle of this candle
  #                   # new_mid = ((float(candle['inth'] + float(candle['intl'])) / 2)
  #                   new_mid = (float(candle['inth']) + float(candle['intl'])) / 2
  #                   current_mid = new_mid
  #                   mid_count += 1
  #                   # If it's the third mid update, close the position
  #                   if mid_count == 3:
  #                       exit_reason = "Third mid detected"
  #                       break
  #               elif stop_loss_condition:
  #                   exit_reason = "Stop loss hit after mid adjustment"
  #                   break

  #       if 'exit_reason' in locals():
  #           # Fetch option data for exit price
  #           option_df = api.get_time_price_series(exchange='NFO', token=option_tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
           
  #           option_df = pd.DataFrame(option_df)
            
  #           option_df['time'] = pd.to_datetime(option_df['time'], format='%d-%m-%Y %H:%M:%S')
  #           print(option_df['time'])

  #           start_time = '2024-11-05 12:50:00'
  #           end_time = '2024-11-05 12:59:00'
  #           filtered_df = option_df[(option_df['time'] >= candle_time) & (option_df['time'] <= candle_time)]
  #           print(f'filterd', filtered_df)
  #           print(f'candle_time', candle_time)
  #           exit_candle = filtered_df
  #           print(f'low',float(exit_candle['intl']))
  #           exit_price = float(exit_candle['intl' if option_type == 'call' else 'inth'])
  #           return candle_time, exit_price, exit_reason

  #       # Check if we've reached the end time
  #       if datetime.now() >= end_timestamp:
  #           return None, None, "Strategy time limit reached"

  #       # Update data for the next iteration if needed
  #       if last_checked_time >= df.index[-1]:
  #           df = api.get_time_price_series(exchange='NSE', token=self.tokenid, starttime=last_checked_time, endtime=end_timestamp, interval=1)
  # def stop_loss_strategy(self, entry_price, option_entry_time, option_type, option_tokenid):
  #   from datetime import datetime, timedelta,time
    
  #   current_mid = entry_price
  #   mid_count = 0
  #   option_entry_time = datetime.strptime(option_entry_time, '%d-%m-%Y %H:%M:%S')
    
  #   last_checked_time = option_entry_time
  #   last_checked_time = last_checked_time.timestamp()
  #   # print(f'option_entry_time: {option_entry_time}')

  #   # Set start time as entry time and end time as 1 hour after entry time
  #   start_timestamp = option_entry_time
  #   end_timestamp = option_entry_time + timedelta(hours=2)
  #   start_timestamp = start_timestamp.timestamp()
  #   end_timestamp = end_timestamp.timestamp()

  #   # Initialize profit targets
  #   initial_target = entry_price * (1.005 if option_type == 'call' else 0.995)  # 0.5% profit target
  #   trailing_target = entry_price * (1.0025 if option_type == 'call' else  0.9999)  # 0.25% trailing target
  #   max_profit_seen = entry_price

  #   # Fetch initial data
  #   df = api.get_time_price_series(exchange='NSE', token=self.tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
    
  #   while True:
  #       new_candles = [row for row in df if datetime.strptime(str(row['time']), '%d-%m-%Y %H:%M:%S').timestamp() > last_checked_time]
  #       new_candles = pd.DataFrame(new_candles)

  #       if new_candles.empty:
  #           if datetime.now() >= end_timestamp:
  #               return None, None, "Strategy time limit reached"
            
  #           time.sleep(60)
  #           df = api.get_time_price_series(exchange='NSE', token=self.tokenid, starttime=last_checked_time, endtime=end_timestamp, interval=1)
  #           continue

  #       for candle_time, candle in new_candles.iterrows():
  #           candle_time = datetime.strptime(candle['time'], '%d-%m-%Y %H:%M:%S')
  #           last_checked_time = candle_time

  #           current_price = float(candle['intc'])  # Using closing price for current price
            
  #           # Update maximum profit seen
  #           if option_type == 'call':
  #               max_profit_seen = max(max_profit_seen, current_price)
  #           else:
  #               max_profit_seen = min(max_profit_seen, current_price)

  #           # Check profit booking conditions
  #           if option_type == 'call':
  #               # Initial target hit
  #               # print('checking Initial target hit')
  #               # if current_price >= initial_target:
  #               #     exit_reason = "Initial profit target reached"
  #               #     break
  #               # Trailing target hit (after price pulls back from high)
  #               if max_profit_seen >= initial_target and current_price <= (max_profit_seen * 0.999):
  #                   # print('checking Trailing profit target hit')
  #                   exit_reason = "Trailing profit target hit"
  #                   break
  #               # Time-based scaling out (after 30 mins if in profit)
  #               if (candle_time >= option_entry_time + timedelta(minutes=30) and 
  #                   current_price >= trailing_target):
  #                   # print('Time-based profit booking')
  #                   exit_reason = "Time-based profit booking"
  #                   break
  #               stop_loss_hit = float(candle['intl']) < entry_price * 0.995
  #           else:  # put
  #               if current_price <= initial_target:
  #                   exit_reason = "Initial profit target reached"
  #                   break
  #               if max_profit_seen <= initial_target and current_price >= (max_profit_seen * 1.001):
  #                   exit_reason = "Trailing profit target hit"
  #                   break
  #               if (candle_time >= option_entry_time + timedelta(minutes=30) and 
  #                   current_price <= trailing_target):
  #                   exit_reason = "Time-based profit booking"
  #                   break
  #               stop_loss_hit = float(candle['inth']) > entry_price * 1.005

  #           if stop_loss_hit:
  #               exit_reason = "Initial stop loss hit"
  #               break

  #           # Start detecting mids only after 5 minutes
  #           if candle_time >= option_entry_time + timedelta(minutes=5):
  #               if option_type == 'call':
  #                   new_mid_condition = float(candle['intl']) > current_mid
  #                   stop_loss_condition = float(candle['inth']) < current_mid * 0.998
  #               else:  # put
  #                   new_mid_condition = float(candle['inth']) < current_mid
  #                   stop_loss_condition = float(candle['inth']) > current_mid * 1.002

  #               if new_mid_condition:
  #                   new_mid = (float(candle['inth']) + float(candle['intl'])) / 2
  #                   current_mid = new_mid
  #                   mid_count += 1
  #                   if mid_count == 3:
  #                       exit_reason = "Third mid detected"
  #                       break
  #               elif stop_loss_condition:
  #                   exit_reason = "Stop loss hit after mid adjustment"
  #                   break

  #       if 'exit_reason' in locals():
  #           option_df = api.get_time_price_series(exchange='NFO', token=option_tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
  #           option_df = pd.DataFrame(option_df)
  #           option_df['time'] = pd.to_datetime(option_df['time'], format='%d-%m-%Y %H:%M:%S')
            
  #           filtered_df = option_df[(option_df['time'] >= candle_time) & (option_df['time'] <= candle_time)]
  #           exit_candle = filtered_df
  #           exit_price = float(exit_candle['intl' if option_type == 'call' else 'inth'])
  #           return candle_time, exit_price, exit_reason
  #       else:
  #           # Define day_end_time at 15:29 on the same day as option_entry_time
  #           day_end_time = datetime.combine(option_entry_time.date(), time(15, 29, 0))
  #           # print(f'start_timestamp', start_timestamp)
  #           # print(f'day_end_time',day_end_time)
            
  #           # Fetch all candles from start time to day end time
  #           option_df = api.get_time_price_series(exchange='NFO', token=option_tokenid, starttime=start_timestamp, endtime=day_end_time.timestamp(), interval=1)
  #           option_df = pd.DataFrame(option_df)
  #           # print(option_df)
  #           # print(option_df.columns)
  #           option_df['time'] = pd.to_datetime(option_df['time'], format='%d-%m-%Y %H:%M:%S')
            
  #           # Sort by time and pick the last available candle before or exactly at 15:29
  #           option_df = option_df.sort_values(by='time')
  #           last_candle = option_df[option_df['time'] <= day_end_time].iloc[-1]  # Select the last row before or at 15:29
            
  #           # Extract the exit price based on the option type
  #           exit_price = float(last_candle['intl' if option_type == 'call' else 'inth'])
  #           exit_time = last_candle['time']
  #           exit_reason = "Exited at day end time (15:29) with last available candle"
            
  #           # Return or use exit time, exit price, and reason as needed
  #           # print("Exit Time:", exit_time)
  #           # print("Exit Price:", exit_price)
  #           # print("Exit Reason:", exit_reason)
            
  #           return exit_time, exit_price, exit_reason

  #       # if datetime.now().timestamp() >= end_timestamp.timestamp():
  #       #     return None, None, "Strategy time limit reached"

  #       # if last_checked_time >= df.index[-1]:
  #       #     df = api.get_time_price_series(exchange='NSE', token=self.tokenid, starttime=last_checked_time, endtime=end_timestamp, interval=1)

  def stop_loss_strategy(self, entry_price, option_entry_time, option_type, option_tokenid):
    from datetime import datetime, timedelta,time
    import talib
    current_mid = entry_price
    mid_count = 0
    option_entry_time = datetime.strptime(option_entry_time, '%d-%m-%Y %H:%M:%S')
    # Create exit time for the same day at 15:00:00
    exit_time = datetime.combine(option_entry_time.date(), time(15, 0, 0))
    
    last_checked_time = option_entry_time
    last_checked_time = last_checked_time.timestamp()
    # print(f'option_entry_time: {option_entry_time}')

    # Set start time as entry time and end time as 1 hour after entry time
    start_timestamp = option_entry_time
    # end_timestamp = option_entry_time + timedelta(hours=2)
  
    start_timestamp = start_timestamp.timestamp()
    end_timestamp = exit_time.timestamp()

    # # Initialize profit targets
    # initial_target = entry_price * (1.005 if option_type == 'call' else 0.995)  # 0.5% profit target
    # trailing_target = entry_price * (1.0025 if option_type == 'call' else  0.9999)  # 0.25% trailing target
    # max_profit_seen = entry_price

    # Fetch initial data
    df1 = api.get_time_price_series(exchange='NSE', token=self.tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)

    def get_market_context(df, lookback=20):
        # print('inside get_market_context')
        # From trailing_mids_v2
        def calculate_atr(df, period):
             # Convert string columns to numeric before calculations
            df['inth'] = pd.to_numeric(df['inth'], errors='coerce')
            df['intl'] = pd.to_numeric(df['intl'], errors='coerce')
            df['intc'] = pd.to_numeric(df['intc'], errors='coerce')
            # print('1')
            high_low = df['inth'] - df['intl']
            # print('2')
            high_close = abs(df['inth'] - df['intc'].shift())
            # print('3')
            low_close = abs(df['intl'] - df['intc'].shift())
            # print('4')
            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            # print('5')
            true_range = ranges.max(axis=1)
            return true_range.rolling(period).mean()
        
        atr = calculate_atr(df, lookback)
        # Convert volume to numeric as well
        df['v'] = pd.to_numeric(df['v'], errors='coerce')
        # print(f'df',df)
        vol_profile = df['v'].mean() * 1.5

        # Convert the Series comparison to a single boolean
        mean_close = df['intc'].mean()
        is_volatile = (atr > mean_close * 0.002).any()  # or .all() depending on your needs
        
        # is_volatile = atr > df['intc'].mean() * 0.002
        return is_volatile, vol_profile, atr

    def calculate_vwap(df):
        # print('in calculate_vwap')
        df['intc'] = pd.to_numeric(df['intc'], errors='coerce')
        df['v'] = pd.to_numeric(df['v'], errors='coerce')
        # From volume_weighted_trailing_stop
        return (df['v'] * df['intc']).cumsum() / df['v'].cumsum()

    # def identify_swing_points(df, window=5):
    #     # From market_structure_stop
    #     print('in identify_swing_points')
    #     df['inth'] = pd.to_numeric(df['inth'], errors='coerce')
    #     df['intl'] = pd.to_numeric(df['intl'], errors='coerce')
    #     swing_high = df['inth'].rolling(window*2+1).apply(
    #         lambda x: x[window] == max(x), raw=True
    #     )
    #     swing_low = df['intl'].rolling(window*2+1).apply(
    #         lambda x: x[window] == min(x), raw=True
    #     )
    #     return swing_high, swing_low

    # def identify_swing_points(df, window=5):
    #     print('in identify_swing_points')
    #     # Convert to numeric
    #     df['inth'] = pd.to_numeric(df['inth'], errors='coerce')
    #     df['intl'] = pd.to_numeric(df['intl'], errors='coerce')
        
    #     # Initialize result series
    #     swing_high = pd.Series(False, index=df.index)
    #     swing_low = pd.Series(False, index=df.index)
        
    #     # Calculate swing points
    #     for i in range(window, len(df) - window):
    #         # Get the window slice
    #         high_window = df['inth'].iloc[i-window:i+window+1]
    #         low_window = df['intl'].iloc[i-window:i+window+1]
            
    #         # Check if current point is swing high/low
    #         if df['inth'].iloc[i] == high_window.max():
    #             swing_high.iloc[i] = True
    #         if df['intl'].iloc[i] == low_window.min():
    #             swing_low.iloc[i] = True
        
    #     return swing_high, swing_low

    def identify_swing_points(df, window=5):
        # print('in identify_swing_points')
        import numpy as np
        
        # Debug prints
        # print(f"Input DataFrame shape: {df.shape}")
        # print("DataFrame columns:", df.columns)
        # print("Sample of inth values:", df['inth'].head())
        # print("Sample of intl values:", df['intl'].head())
        
        # Check if DataFrame is too small
        if len(df) <= window * 2:
            # Return empty series if not enough data
            return pd.Series(False, index=df.index), pd.Series(False, index=df.index)
        
        try:
            # Ensure data is numeric and handle any conversion issues
            highs = df['inth'].astype(float).values
            lows = df['intl'].astype(float).values
            
            # Create boolean arrays
            swing_high = np.zeros(len(df), dtype=bool)
            swing_low = np.zeros(len(df), dtype=bool)
            
            # Process each point
            for i in range(window, len(df) - window):
                try:
                    # Get current values
                    curr_high = highs[i]
                    curr_low = lows[i]
                    
                    # Get window values
                    high_window = highs[i-window:i+window+1]
                    low_window = lows[i-window:i+window+1]
                    
                    # Check for swing points
                    if not np.isnan(curr_high) and curr_high == np.nanmax(high_window):
                        swing_high[i] = True
                    if not np.isnan(curr_low) and curr_low == np.nanmin(low_window):
                        swing_low[i] = True
                        
                except Exception as e:
                    print(f"Error processing point {i}: {str(e)}")
                    continue
            
            # Convert to pandas Series
            swing_high_series = pd.Series(swing_high, index=df.index)
            swing_low_series = pd.Series(swing_low, index=df.index)
            
            print(f"Successfully identified swing points. Highs: {sum(swing_high)}, Lows: {sum(swing_low)}")
            
            return swing_high_series, swing_low_series
            
        except Exception as e:
            print(f"Error in identify_swing_points: {str(e)}")
            # Return empty series in case of error
            return pd.Series(False, index=df.index), pd.Series(False, index=df.index)
    #     in identify_swing_points
# Error processing KOTAKBANK: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
# Error occurred on line 1519: raise ValueError(
# completed

    # Additional helper functions for new strategies
    def detect_momentum_shift(df, lookback=5):
        # print('in detect_momentum_shift')
        df['inth'] = pd.to_numeric(df['inth'], errors='coerce')
        df['intl'] = pd.to_numeric(df['intl'], errors='coerce')
        df['intc'] = pd.to_numeric(df['intc'], errors='coerce')
        df['into'] = pd.to_numeric(df['into'], errors='coerce')  # Added this line
        recent_range = df.tail(lookback)
        body_sizes = abs(recent_range['intc'] - recent_range['into'])
        wicks = recent_range['inth'] - recent_range['intl']
         # Calculate means and compare
        current_body_mean = body_sizes.mean()
        prev_body_mean = body_sizes.shift(lookback).mean()
        current_wick_mean = wicks.mean()
        prev_wick_mean = wicks.shift(lookback).mean()
        
        # Add debug prints
        # print(f"Current body mean: {current_body_mean}")
        # print(f"Previous body mean: {prev_body_mean}")
        # print(f"Current wick mean: {current_wick_mean}")
        # print(f"Previous wick mean: {prev_wick_mean}")
        
        # Return the momentum shift condition
        return (pd.notna(current_body_mean) and 
                pd.notna(prev_body_mean) and 
                pd.notna(current_wick_mean) and 
                pd.notna(prev_wick_mean) and 
                current_body_mean < prev_body_mean and 
                current_wick_mean > prev_wick_mean)
        # return (body_sizes.mean() < body_sizes.shift(lookback).mean() and 
        #         wicks.mean() > wicks.shift(lookback).mean())

    def find_psych_levels(price, range_percent=1.0):
        # print('in find_psych_levels')
        base = round(price, -1)
        levels = []
        for i in range(-3, 4):
            level = base + (i * 10)
            if abs(level - price) / price <= range_percent/100:
                levels.append(level)
        return levels

    def detect_level_rejection(candle, level, option_type):
        # print('in detect_level_rejection')
        # df['inth'] = pd.to_numeric(df['inth'], errors='coerce')
        # df['intl'] = pd.to_numeric(df['intl'], errors='coerce')
        # df['intc'] = pd.to_numeric(df['intc'], errors='coerce')
        x=1
        return (abs(float(candle['inth' if option_type == 'call' else 'intl']) - level) / level <= 0.001 and
                float(candle['intc']) < float(candle['into']) if option_type == 'call' else float(candle['intc']) > float(candle['into']))

    def calculate_volatility(df, window=20):
        print('in calculate_volatility')
        df['inth'] = pd.to_numeric(df['inth'], errors='coerce')
        df['intl'] = pd.to_numeric(df['intl'], errors='coerce')
        # df['intc'] = pd.to_numeric(df['intc'], errors='coerce')
        return (df['inth'] - df['intl']).rolling(window).std()

    def identify_swing_pattern(df, window=5):
        # print('in identify_swing_pattern')
        df['inth'] = pd.to_numeric(df['inth'], errors='coerce')
        df['intl'] = pd.to_numeric(df['intl'], errors='coerce')
        df['intc'] = pd.to_numeric(df['intc'], errors='coerce')
        highs = df['inth'].rolling(window*2+1).apply(
            lambda x: x[window] == max(x), raw=True
        )
        lows = df['intl'].rolling(window*2+1).apply(
            lambda x: x[window] == min(x), raw=True
        )
        return highs, lows

    # Initialize tracking variables for all strategies
    current_mid = entry_price
    mid_count = 0
    vwap_crosses = 0
    structure_breaks = 0
    trailing_stop = entry_price * (0.98 if option_type == 'call' else 1.02)
    consec_crosses = 0
    
    # Initialize initial tracking variables from original strategy
    max_profit_seen = entry_price
    initial_target = entry_price * (1.005 if option_type == 'call' else 0.995)
    trailing_target = entry_price * (1.0015 if option_type == 'call' else 0.9985)

      # Initialize additional tracking variables
    momentum_shifts = 0
    level_rejections = 0
    vol_expansions = 0
    swing_failures = 0
    psych_levels = find_psych_levels(entry_price)
    prev_swing = None

#       in find_psych_levels
# before Get market context and indicators
# inside get_market_context
# Error processing KOTAKBANK: cannot convert the series to <class 'float'>
# Error occurred on line 230: raise TypeError(f"cannot convert the series to {converter}")
# completed

    
    
    while True:
        # new_candles = [row for row in df1 if datetime.strptime(str(row['time']), '%d-%m-%Y %H:%M:%S').timestamp() > last_checked_time]
        # new_candles = pd.DataFrame(new_candles)

        # if new_candles.empty:
            
        #     if datetime.now() >= end_timestamp:
        #         return None, None, "Strategy time limit reached"
            
        #     time.sleep(60)
        #     df = api.get_time_price_series(exchange='NSE', token=self.tokenid, starttime=last_checked_time, endtime=end_timestamp, interval=1)
        #     continue
        new_candles = [row for row in df1 if datetime.strptime(str(row['time']), '%d-%m-%Y %H:%M:%S').timestamp() > last_checked_time]
        # print(f'new_candles', new_candles)
        new_candles = pd.DataFrame(new_candles)

        # Add this line to sort
        new_candles = new_candles.sort_values('time', ascending=True).reset_index(drop=True)
        # print('inside stop loss')
        # print(f'new_candles', new_candles)
        # print('inside stop loss')
        # print(f'new_candles', new_candles)
        # import pandas as pd
        # import talib
        # import datetime
        
        # Convert to DataFrame
        df_candles = pd.DataFrame(new_candles)
        
        # Convert to numeric, handling errors
        for col in ['into', 'inth', 'intl', 'intc', 'v']:
            df_candles[col] = pd.to_numeric(df_candles[col], errors='coerce')
        
        df_candles['time'] = pd.to_datetime(df_candles['time'], format='%d-%m-%Y %H:%M:%S')
        df_candles = df_candles.set_index('time')
        
        # # Calculate indicators
        # try:
        #     df_candles['SMA_10'] = talib.SMA(df_candles['intc'], timeperiod=10)
        #     df_candles['EMA_20'] = talib.EMA(df_candles['intc'], timeperiod=20)
        #     df_candles['RSI'] = talib.RSI(df_candles['intc'], timeperiod=14)
        #     df_candles['ADX'] = talib.ADX(df_candles['inth'], df_candles['intl'], df_candles['intc'], timeperiod=14)
        #     df_candles['MACD'], df_candles['MACD_signal'], df_candles['MACD_hist'] = talib.MACD(df_candles['intc'], fastperiod=12, slowperiod=26, signalperiod=9)
        #     df_candles['ATR'] = talib.ATR(df_candles['inth'], df_candles['intl'], df_candles['intc'], timeperiod=14)
        #     df_candles['BB_UPPER'], df_candles['BB_MIDDLE'], df_candles['BB_LOWER'] = talib.BBANDS(df_candles['intc'], timeperiod=20)
        #     df_candles['OBV'] = talib.OBV(df_candles['intc'], df_candles['v'])
        # except Exception as e:
        #     print(f"Error calculating technical indicators: {e}")
        
        # Specific Time Calculation
        # target_time = datetime(2024, 11, 29, 14, 57, 0)  # Example: 29-11-2024 14:57:00
        
        # if target_time in df_candles.index:
        #     indicators_at_time = df_candles.loc[target_time]
        #     print(f"\nIndicators at {target_time}:\n{indicators_at_time}")
        # else:
        #     print(f"\nNo data found for {target_time}")
        
        # Print the entire DataFrame with indicators
        # print("\nDataFrame with all indicators:\n", df_candles)
        
        # Example of accessing a specific indicator at a specific time:
        # if target_time in df_candles.index and 'RSI' in df_candles.columns:
        #     rsi_at_time = df_candles.loc[target_time, 'RSI']
        #     print(f"\nRSI at {target_time}: {rsi_at_time}")
        
        
        
        if new_candles.empty:
            if datetime.now() >= end_timestamp:
                return None, None, "Strategy time limit reached"
            
            time.sleep(60)
            df = api.get_time_price_series(exchange='NSE', token=self.tokenid, starttime=last_checked_time, endtime=end_timestamp, interval=1)
            continue
        # print('before Get market context and indicators')
        # Get market context and indicators
        is_volatile, volume_threshold, atr = get_market_context(new_candles)
        new_vwap = calculate_vwap(new_candles)
        swing_high, swing_low = identify_swing_points(new_candles)
        # print(f'is_volatile',is_volatile)
        stop_loss_percent = 0.003 if is_volatile else 0.001

        for candle_time, candle in new_candles.iterrows():
            # print('in for loop')
            candle_time = datetime.strptime(candle['time'], '%d-%m-%Y %H:%M:%S')
            last_checked_time = candle_time
            # print(f'candle_time',candle_time)
            current_price = float(candle['intc'])  # Using closing price for current price
            
           
            # print('checking 0 Initial stop loss hit - Highest priority exit to minimize losses')
            if option_type == 'call':
                # stop_loss_hit = float(candle['intl']) < entry_price * 0.997
                stop_loss_hit = round(float(candle['intl']), 3) < round(entry_price * 0.997, 3)
            else:
                # stop_loss_hit = float(candle['inth']) > entry_price * 1.003
                stop_loss_hit = round(float(candle['inth']), 3) > round(1.003 * entry_price, 3)

            if stop_loss_hit:
                exit_reason = "Initial stop loss hit"
                return candle_time, current_price, "Initial stop loss hit"
                # break

            # print('checking 1 Add volatility expansion checks')
            # current_vol = float(candle['inth']) - float(candle['intl'])
            # avg_vol = calculate_volatility(new_candles).mean()
            # if current_vol > avg_vol * 1.5:
            #     vol_expansions += 1
            #     vol_condition = (
            #         float(candle['intc']) < float(candle['into']) if option_type == 'call'
            #         else float(candle['intc']) > float(candle['into'])
            #     )
            #     if vol_condition:
            #         return candle_time, current_price, f"{'Bearish' if option_type == 'call' else 'Bullish'} volatility expansion"

            # print('checking 2 Add momentum reversal checks')
            if detect_momentum_shift(new_candles):
                print('sucess 2')
                momentum_condition = (
                    float(candle['intc']) < float(candle['into']) if option_type == 'call'
                    else float(candle['intc']) > float(candle['into'])
                )
                if momentum_condition:
                    momentum_shifts += 1
                    if momentum_shifts >= 2:
                        return candle_time, current_price, "Multiple momentum shifts detected"

            # print('checking 3 Profit taking conditions based on price movement')
             # Update maximum profit seen
            if option_type == 'call':
                max_profit_seen = max(max_profit_seen, candle['intc'])
            else:
                max_profit_seen = min(max_profit_seen, candle['intc'])
                
            if option_type == 'call':
                if max_profit_seen >= initial_target and round(current_price,4) <= round(max_profit_seen * 0.9985,4):
                    return candle_time, current_price, "Lock profits on pullback from peak"
            else:
                if max_profit_seen <= initial_target and round(current_price,4) >= round(max_profit_seen * 1.0025,4):
                    return candle_time, current_price, "Lock profits on pullback from trough"

            # print('checking 4 market_structure_stop conditions')
            if option_type == 'call':
                structure_break = (
                    swing_low.get(candle_time, False) and 
                    float(candle['intl']) < current_mid * (1 - stop_loss_percent)
                )
            else:
                structure_break = (
                    swing_high.get(candle_time, False) and 
                    float(candle['inth']) > current_mid * (1 + stop_loss_percent)
                )

            # print('checking Structure breaks (major trend change)')
            if structure_break:
                structure_breaks += 1
                if structure_breaks >= 2:
                    return candle_time, current_price, "Multiple structure breaks - Major trend change"

            
            # if structure_break:
            #     structure_breaks += 1

              # if structure_breaks >= 2:
            #     exit_reason = "Market structure broken"
            #     break


            # print('checking 5 Add swing failure checks')
            swing_highs, swing_lows = identify_swing_pattern(new_candles)
            if option_type == 'call' and swing_highs.iloc[-1]:
                if prev_swing and float(candle['inth']) > prev_swing * 1.001 and float(candle['intc']) < prev_swing:
                    swing_failures += 1
                prev_swing = float(candle['inth'])
            elif option_type == 'put' and swing_lows.iloc[-1]:
                if prev_swing and float(candle['intl']) < prev_swing * 0.999 and float(candle['intc']) > prev_swing:
                    swing_failures += 1
                prev_swing = float(candle['intl'])

            if swing_failures >= 2:
                return candle_time, current_price, "Multiple swing failures detected"

            # print('checking 6 Time-based exits with profit')
            # if (candle_time >= option_entry_time + timedelta(minutes=30) and 
            #     ((option_type == 'call' and current_price >= trailing_target) or
            #      (option_type == 'put' and current_price <= trailing_target))):
            #     return candle_time, current_price, "Secured time-based profits"

            # print('checking 7 Add psychological level checks')
            # for level in psych_levels:
            #     if detect_level_rejection(candle, level, option_type):
            #         level_rejections += 1
            #         if level_rejections >= 2:
            #             return candle_time, current_price, "Multiple psychological level rejections"

            # print(candle)
            # print(f'new_vwap',new_vwap)
            # print(f'volume_threshold',volume_threshold)
            

            # print('checking 8 volume_weighted_trailing_stop conditions')
            if option_type == 'call':
                vwap_condition = (
                   
                    (current_price > new_vwap).any() and
                    float(candle['v']) > volume_threshold
                )
            else:
                vwap_condition = (
                    (current_price < new_vwap).any() and
                    float(candle['v']) > volume_threshold
                )

            # print('checking VWAP trend confirmation (slower but reliable)')
            if vwap_condition:
                consec_crosses += 1
                if consec_crosses >= 3:
                    return candle_time, current_price, "Strong VWAP trend established"

            # if vwap_condition:
            #     consec_crosses += 1
            # else:
            #     consec_crosses = 0

            # if consec_crosses >= 3:
            #     exit_reason = "Strong VWAP trend confirmed"
            #     break


            
            # if mids_condition:
            #     current_mid = (float(candle['inth']) + float(candle['intl'])) / 2
            #     trailing_stop = current_mid * ((1 - stop_loss_percent) if option_type == 'call' else (1 + stop_loss_percent))
            #     mid_count += 1
            # if mid_count >= 3:
            #     exit_reason = "Trend confirmed with 3 mids"
            #     break

            # # print('checking 9 trailing_mids_v2 strategy conditions')
            # if option_type == 'call':
            #     mids_condition = (
            #         float(candle['intl']) > current_mid and  # Higher low
            #         float(candle['intc']) > float(candle['into']) and  # Bullish close
            #         float(candle['v']) > volume_threshold  # Volume confirmation
            #     )
            #     mids_stop_hit = float(candle['intl']) < trailing_stop
            # else:
            #     mids_condition = (
            #         float(candle['inth']) < current_mid and  # Lower high
            #         float(candle['intc']) < float(candle['into']) and  # Bearish close
            #         float(candle['v']) > volume_threshold  # Volume confirmation
            #     )
            #     mids_stop_hit = float(candle['inth']) > trailing_stop
            # if mids_stop_hit:
            #     return candle_time, current_price, "Trailing mid-point stop triggered"

            # # print('checking 10 Mid-point based trailing (slowest but confirming trend)')
            # if mids_condition:
            #     current_mid = (float(candle['inth']) + float(candle['intl'])) / 2
            #     trailing_stop = current_mid * ((1 - stop_loss_percent) if option_type == 'call' else (1 + stop_loss_percent))
            #     mid_count += 1
            #     if mid_count >= 3:
            #         return candle_time, current_price, "Strong trend confirmation with multiple mids"


           
                

            #  # Combined exit conditions
            # if mids_stop_hit:
            #     exit_reason = "Trailing mids stop loss hit"
            #     break


          

            

        
           

            # Update strategy counters and stops
            # 

            
            
            

            

          

            # Check profit booking conditions
            # if option_type == 'call':
                # Initial target hit
                # print('checking Initial target hit')
                # if current_price >= initial_target:
                #     exit_reason = "Initial profit target reached"
                #     break
                # Trailing target hit (after price pulls back from high)
                # if max_profit_seen >= initial_target and current_price <= (max_profit_seen * 0.999):
                #     # print('checking Trailing profit target hit')
                #     exit_reason = "Trailing profit target hit"
                #     break
                # Time-based scaling out (after 30 mins if in profit)
                # if (candle_time >= option_entry_time + timedelta(minutes=30) and 
                #     current_price >= trailing_target):
                #     # print('Time-based profit booking')
                #     exit_reason = "Time-based profit booking"
                #     break
                # stop_loss_hit = float(candle['intl']) < entry_price * 0.995
            # else:  # put
                # if current_price <= initial_target:
                #     exit_reason = "Initial profit target reached"
                #     break
                # if max_profit_seen <= initial_target and current_price >= (max_profit_seen * 1.001):
                #     exit_reason = "Trailing profit target hit"
                #     break
                # if (candle_time >= option_entry_time + timedelta(minutes=30) and 
                #     current_price <= trailing_target):
                #     exit_reason = "Time-based profit booking"
                #     break
                # stop_loss_hit = float(candle['inth']) > entry_price * 1.005

            # if stop_loss_hit:
            #     exit_reason = "Initial stop loss hit"
            #     break

            # # print('checking Start detecting mids only after 5 minutes')
            # if candle_time >= option_entry_time + timedelta(minutes=5):
            #     if option_type == 'call':
            #         new_mid_condition = float(candle['intl']) > current_mid
            #         stop_loss_condition = float(candle['inth']) < current_mid * 0.998
            #     else:  # put
            #         new_mid_condition = float(candle['inth']) < current_mid
            #         stop_loss_condition = float(candle['inth']) > current_mid * 1.002

            #     if new_mid_condition:
            #         new_mid = (float(candle['inth']) + float(candle['intl'])) / 2
            #         current_mid = new_mid
            #         mid_count += 1
            #         if mid_count == 3:
            #             exit_reason = "Third mid detected"
            #             break
            #     elif stop_loss_condition:
            #         exit_reason = "Stop loss hit after mid adjustment"
            #         break

        if 'exit_reason' in locals():
            option_df = api.get_time_price_series(exchange='NFO', token=option_tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
            option_df = pd.DataFrame(option_df)
            option_df['time'] = pd.to_datetime(option_df['time'], format='%d-%m-%Y %H:%M:%S')
            
            filtered_df = option_df[(option_df['time'] >= candle_time) & (option_df['time'] <= candle_time)]
            exit_candle = filtered_df
            exit_price = float(exit_candle['intl' if option_type == 'call' else 'inth'])
            return candle_time, exit_price, exit_reason
        else:
            # Define day_end_time at 15:29 on the same day as option_entry_time
            day_end_time = datetime.combine(option_entry_time.date(), time(14, 59, 0))
            # print(f'start_timestamp', start_timestamp)
            # print(f'day_end_time',day_end_time)
            
            # Fetch all candles from start time to day end time
            option_df = api.get_time_price_series(exchange='NFO', token=option_tokenid, starttime=start_timestamp, endtime=day_end_time.timestamp(), interval=1)
            option_df = pd.DataFrame(option_df)
            # print(option_df)
            # print(option_df.columns)
            option_df['time'] = pd.to_datetime(option_df['time'], format='%d-%m-%Y %H:%M:%S')
            
            # Sort by time and pick the last available candle before or exactly at 15:29
            option_df = option_df.sort_values(by='time')
            last_candle = option_df[option_df['time'] <= day_end_time].iloc[-1]  # Select the last row before or at 15:29
            
            # Extract the exit price based on the option type
            exit_price = float(last_candle['intl' if option_type == 'call' else 'inth'])
            exit_time = last_candle['time']
            exit_reason = "Exited at day end time (14:59) with last available candle"
            
            # Return or use exit time, exit price, and reason as needed
            # print("Exit Time:", exit_time)
            # print("Exit Price:", exit_price)
            # print("Exit Reason:", exit_reason)
            
            return exit_time, exit_price, exit_reason
  def get_option_price_by_time(self,target_datetime_str, tokenid=''):
        import datetime as datetime
        # print('inside get_option_price_by_time')
        current_price=''
        current_volume=''
        current_time=''
        tokenid=tokenid
        # target_datetime_str
        target_datetime = target_datetime_str
        start_datetime = target_datetime - datetime.timedelta(seconds=60)
        end_datetime = target_datetime + datetime.timedelta(seconds=60)
        # print(f'start_datetime', start_datetime)
        # print(f'end_datetime', end_datetime)
        date_input = start_datetime.strftime("%d-%m-%Y")
        starttime_input = start_datetime.strftime("%H:%M")
        endtime_input = end_datetime.strftime("%H:%M")
        start_timestamp, end_timestamp = self.get_start_end_timestamps(date_input, starttime_input, endtime_input)
        df = api.get_time_price_series(exchange='NFO', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
        if isinstance(df, list) and len(df) > 0:
            target_candle = None
            for candle in df:
                if isinstance(candle, dict) and 'time' in candle:
                    candle_time = datetime.datetime.strptime(candle['time'], "%d-%m-%Y %H:%M:%S")
                    # print(f'candle_time', candle_time)
                    # print(f'target_datetime', target_datetime)
                    if candle_time == target_datetime:
                        target_candle = candle
                        break
            if target_candle and isinstance(target_candle, dict) and 'intc' in target_candle and 'intv' in target_candle:
                current_price = float(target_candle['intc'])
                current_volume = int(target_candle['intv'])
                current_time = target_candle['time']
            else:
                x=1
                # print(f"Unable to find data for the specific time: {target_datetime_str}")
        else:
            x=1
            # print("Invalid or empty response from get_time_price_series")
        return current_price, current_time
  def run(self, signal_reversal):
    # print('inside run')
    
    position = np.zeros(self.data.shape[0])
    cash = position.copy() 
    pnl = position.copy()  # Add array to track P&L
    option_type = ['']*len(position)
    option_tokenid = ['']*len(position)
    option_entry_price = ['']*len(position)
    option_entry_time = ['']*len(position)
    global last_stock_traded
        # last_stock_traded=self.ticker
    global last_stock_traded_time
        # last_stock_traded_time=exit_time
    global stop_loss_reason
        # stop_loss_reason=reason
    global reversed_signal
        # reversed_signal = 0
      # Add a flag to track if we're in an opposite position
    is_opposite_position = False
    for i, (ts, row) in enumerate(self.data.iterrows()):

        if is_opposite_position == True:
            print('back from continue')
        
        # print('inside run for loop')
        if i == 0:
                cash[i] = self.starting_capital
                pnl[i] = 0  # Initialize P&L as 0
                continue

        
        #  # Carry forward previous values
        # position[i] = position[i-1]
        # cash[i] = cash[i-1]  # Add this line to carry forward cash
        # option_type[i] = option_type[i-1]
        # pnl[i] = pnl[i-1]  # Carry forward P&L
        # option_tokenid[i] = option_tokenid[i-1]  # Add this line to carry forward token
        # option_entry_price[i] = option_entry_price[i-1]  # Add this line to carry forward entry price
        # option_entry_time[i] = option_entry_time[i-1]  # Add this line to carry forward entry time
        

        if reversed_signal is not None and (reversed_signal == 1 or reversed_signal == -1):
            print(f'signal due to reversal: ', reversed_signal)
            signal = reversed_signal
           
        else:
             print('no signal reversal initial')
             signal = self._getSignal()
                
            
        # print(f'signal insiide run', signal)
        self.current_price = self.set_current_price()
        # print(f'row', row)
        # print(f'ts', ts)
        if signal != 0 and option_type[i] == '' and position[i] ==0:
            option_result, actual_option = self.get_option_data(signal)
            print('inside initital buy')
            print(f'actual_option', actual_option)
            if actual_option is not None:
                option_price = actual_option['price']
                option_strike=actual_option['strike']
                position[i] = 1
                # cash[i] -= position[i] * option_price
                cash[i] -= position[i] * option_price  # Deduct option cost from cash
                option_type[i] = 'call' if signal > 0 else 'put'
                if option_type[i] == 'call':
                    option_tokenid[i] = option_result.get('CE')['token']
                elif option_type[i] == 'put':
                    option_tokenid[i] = option_result.get('PE')['token']
                option_entry_price[i] = option_price  # Store the option entry price
                entry_price = self.current_price
                option_entry_price[i] = entry_price
                entry_time = actual_option['time']
                option_entry_time[i] = entry_time
                print(f"Position opened at {entry_time}")
                print(f"opening price: {entry_price}")
                print(f"option opening price: {option_price}")
            else:
                x=1
                break
                # print("No valid options found, skipping trade")
        if position[i] > 0:
            print('inside stop loss')
            if is_opposite_position == True:
                print('check becuase of new open positiion after initial stop loss')
            # print('checking exit price')
            exit_time, exit_price, reason = self.stop_loss_strategy(option_entry_price[i], option_entry_time[i], option_type[i], option_tokenid[i])
            # print(f'exit_time', exit_time)
            # print(f'exit_price', exit_price)
            # print(f'reason', reason)
            # cash[i] += position[i] * exit_price
            option_exit_price, option_exit_time = self.get_option_price_by_time(exit_time, option_tokenid[i])

             # Calculate P&L for this trade
            # trade_pnl = (option_exit_price - option_entry_price[i]) * position[i]
            # pnl[i] += trade_pnl  # Add trade P&L to cumulative P&L
            cash[i] += position[i] * option_exit_price  # Add proceeds from closing position
            lot_size = self.data['lot_size']
            underlying_price = self.data['option_underlying_price']
            # print(f'lot_size',lot_size)
            # Convert numpy arrays/pandas elements to simple values
            if hasattr(lot_size, 'iloc'):
                lot_size = lot_size.iloc[0]
            elif hasattr(lot_size, '__array__'):
                lot_size = lot_size[0]
                
            if hasattr(option_price, 'iloc'):
                option_price = option_price.iloc[0]
            elif hasattr(option_price, '__array__'):
                option_price = option_price[0]
                
            if hasattr(option_exit_price, 'iloc'):
                option_exit_price = option_exit_price.iloc[0]
            elif hasattr(option_exit_price, '__array__'):
                option_exit_price = option_exit_price[0]
            
            # Convert to float
            lot_size = float(lot_size)
            option_price = float(option_price)
            option_exit_price = float(option_exit_price)
            
            # Calculate PnL
            pnl = lot_size * (option_exit_price - option_price)
                
            #     # Reset position flags
            # position[i] = 0
            # option_type[i] = ''
            # option_tokenid[i] = ''
                
            print(f'option_exit_price', option_exit_price)
            print(f'option_exit_time', option_exit_time)
            
            position[i] = 0
            # option_type[i] = ''
            # option_tokenid[i] = ''
            print(f"Position closed at {exit_time}")
            print(f"Exit price: {exit_price}")
            print(f"Option Exit price: {option_exit_price}")
            print(f"Reason: {reason}")

            # self.data['position'] = position
            # self.data['cash'] = cash
            # self.data['option_type'] = actual_option['type']
            # self.data['pnl'] = pnl  # Add P&L column
            # self.data['option_tokenid'] = option_tokenid  # Add this line
            # self.data['option_entry_price'] = option_price  # Add this line
            # self.data['option_entry_time'] = entry_time  # Add this line
            # self.data['option_exit_price'] = option_exit_price  # Add this line
            # self.data['option_exit_time'] = option_exit_time  # Add this line
            # self.data['option_exit_reason'] = reason  # Add this line
            # self.data['option_underlying_price'] = underlying_price  # Add this line
            # self.data['option_lot_size'] = lot_size
            # self.data['option_entry_underlying_price']= entry_price
            # self.data['option_entry_strike'] = option_strike
            # self.data['portfolio'] = self.data['cash']
            # print('calulating returns inside stop loss')
            # self.data = calcReturns(self.data)
            
            # if reason == 'Initial stop loss hit':
            #     print('inside Initial stop loss hit ')
            #     # Set flag for opposite position
            #     is_opposite_position = True
                
            #     # Determine opposite option type
            #     new_option_type = 'put' if option_type[i] == 'call' else 'call'
            #     new_signal = -1 if new_option_type == 'put' else 1
                
            #     # Get option data for opposite position
            #     option_result, actual_option = self.get_option_data(new_signal)
                
            #     if actual_option is not None:
            #         print('inside Initial stop loss hit opening position ')
            #         option_price = actual_option['price']
            #         option_strike=actual_option['strike']
                    
            #         # Reset position for new entry
            #         position[i] = 1
            #         cash[i] -= position[i] * actual_option['price']
            #         option_type[i] = new_option_type
                    
            #         if new_option_type == 'call':
            #             option_tokenid[i] = option_result.get('CE')['token']
            #         else:
            #             option_tokenid[i] = option_result.get('PE')['token']
                    
            #         # Update entry details
            #         entry_price = self.current_price
            #         option_entry_price[i] = entry_price
            #         option_entry_time[i] = actual_option['time']
                    
            #         print(f"Opened opposite {new_option_type} position at {option_entry_time[i]}")
            #         print(f"New opening price: {entry_price}")
            #         print(f"New option opening price: {actual_option['price']}")


            #         # option_price = actual_option['price']
            #         # option_strike=actual_option['strike']
            #         # position[i] = 1
            #         # # cash[i] -= position[i] * option_price
            #         # cash[i] -= position[i] * option_price  # Deduct option cost from cash
            #         # option_type[i] = 'call' if signal > 0 else 'put'
            #         # if option_type[i] == 'call':
            #         #     option_tokenid[i] = option_result.get('CE')['token']
            #         # elif option_type[i] == 'put':
            #         #     option_tokenid[i] = option_result.get('PE')['token']
            #         # option_entry_price[i] = option_price  # Store the option entry price
            #         # entry_price = self.current_price
            #         # option_entry_price[i] = entry_price
            #         # entry_time = actual_option['time']
            #         # option_entry_time[i] = entry_time
            #         # print(f"Position opened at {entry_time}")
            #         # print(f"opening price: {entry_price}")
            #         # print(f"option opening price: {option_price}")

            #         print('inside Initial stop loss conitnuing after opening position')
               
                    
                    

                    

            #         if position[i] > 0:
            #             print('inside Initial stop loss checking stop loss')
            #             if is_opposite_position == True:
            #                 print('check becuase of new open positiion after initial stop loss')
            #             # print('checking exit price')
            #             exit_time, exit_price, reason = self.stop_loss_strategy(option_entry_price[i], option_entry_time[i], option_type[i], option_tokenid[i])
            #             # print(f'exit_time', exit_time)
            #             # print(f'exit_price', exit_price)
            #             # print(f'reason', reason)
            #             # cash[i] += position[i] * exit_price
            #             option_exit_price, option_exit_time = self.get_option_price_by_time(exit_time, option_tokenid[i])
            
            #              # Calculate P&L for this trade
            #             # trade_pnl = (option_exit_price - option_entry_price[i]) * position[i]
            #             # pnl[i] += trade_pnl  # Add trade P&L to cumulative P&L
            #             cash[i] += position[i] * option_exit_price  # Add proceeds from closing position
            #             lot_size = self.data['lot_size']
            #             underlying_price = self.data['option_underlying_price']
            #             # print(f'lot_size',lot_size)
            #             # Convert numpy arrays/pandas elements to simple values
            #             if hasattr(lot_size, 'iloc'):
            #                 lot_size = lot_size.iloc[0]
            #             elif hasattr(lot_size, '__array__'):
            #                 lot_size = lot_size[0]
                            
            #             if hasattr(option_price, 'iloc'):
            #                 option_price = option_price.iloc[0]
            #             elif hasattr(option_price, '__array__'):
            #                 option_price = option_price[0]
                            
            #             if hasattr(option_exit_price, 'iloc'):
            #                 option_exit_price = option_exit_price.iloc[0]
            #             elif hasattr(option_exit_price, '__array__'):
            #                 option_exit_price = option_exit_price[0]
                        
            #             # Convert to float
            #             lot_size = float(lot_size)
            #             option_price = float(option_price)
            #             option_exit_price = float(option_exit_price)
                        
            #             # Calculate PnL
            #             pnl = lot_size * (option_exit_price - option_price)
                            
            #             #     # Reset position flags
            #             # position[i] = 0
            #             # option_type[i] = ''
            #             # option_tokenid[i] = ''
                            
            #             print(f'option_exit_price', option_exit_price)
            #             print(f'option_exit_time', option_exit_time)
                        
            #             position[i] = 0
            #             # option_type[i] = ''
            #             # option_tokenid[i] = ''
            #             print(f"Position closed at {exit_time}")
            #             print(f"Exit price: {exit_price}")
            #             print(f"Option Exit price: {option_exit_price}")
            #             print(f"Reason: {reason}")
            
            #             self.data['position'] = position
            #             self.data['cash'] = cash
            #             self.data['option_type'] = actual_option['type']
            #             self.data['pnl'] = pnl  # Add P&L column
            #             self.data['option_tokenid'] = option_tokenid  # Add this line
            #             self.data['option_entry_price'] = option_price  # Add this line
            #             self.data['option_entry_time'] = entry_time  # Add this line
            #             self.data['option_exit_price'] = option_exit_price  # Add this line
            #             self.data['option_exit_time'] = option_exit_time  # Add this line
            #             self.data['option_exit_reason'] = reason  # Add this line
            #             self.data['option_underlying_price'] = underlying_price  # Add this line
            #             self.data['option_lot_size'] = lot_size
            #             self.data['option_entry_underlying_price']= entry_price
            #             self.data['option_entry_strike'] = option_strike
            #             self.data['portfolio'] = self.data['cash']
            #             print('calulating returns inside initial stop loss after stop loss')
            #             self.data = calcReturns(self.data)

            #         # print("No valid options found, skipping trade")
            #         continue  # Continue monitoring the opposite position
            # else:
                # Reset position and flags if not opening opposite position
                # is_opposite_position = False
                # position[i] = 0
                # option_type[i] = ''
                # option_tokenid[i] = ''
            if option_price is not None:
                    print('breaking for loop')
                    break
            



            
        # global last_stock_traded
    last_stock_traded=self.ticker
    print(f'last_stock_traded',last_stock_traded)
        # global last_stock_traded_time
    last_stock_traded_time=exit_time
    print(f'last_stock_traded_time', last_stock_traded_time)
        # global stop_loss_reason
    stop_loss_reason=reason
    print(f'stop_loss_reason', stop_loss_reason)
        # global reversed_signal
    reversed_signal = 0
    print(f'reversed_signal', reversed_signal)
    print(reason)

    if reason == 'Initial stop loss hit':
            if signal == 1:
                reversed_signal = -1
                print(f'reversed_signal', reversed_signal)
            elif signal == -1:
                reversed_signal = 1
                print(f'reversed_signal', reversed_signal)
            else:
                reversed_signal = 0
                print(f'reversed_signal', reversed_signal)
                
                
        

    technicals = self.calculate_technicals_at_times(entry_time,option_exit_time)
    print(f'technicals',technicals)
    
       

    print('back to main run, finalizaing')
    print('calulating returns finalizaing')
    self.data['position'] = position
    self.data['cash'] = cash
    self.data['option_type'] = actual_option['type']
    self.data['pnl'] = pnl  # Add P&L column
    self.data['option_tokenid'] = option_tokenid  # Add this line
    self.data['option_entry_price'] = option_price  # Add this line
    self.data['option_entry_time'] = entry_time  # Add this line
    self.data['option_exit_price'] = option_exit_price  # Add this line
    self.data['option_exit_time'] = option_exit_time  # Add this line
    self.data['option_exit_reason'] = reason  # Add this line
    self.data['option_underlying_price'] = underlying_price  # Add this line
    self.data['option_lot_size'] = lot_size
    self.data['option_entry_underlying_price']= entry_price
    self.data['option_entry_strike'] = option_strike
    self.data['entry_SMA_10'] = technicals.get('entry_SMA_10', None)
    self.data['entry_EMA_20'] = technicals.get('entry_EMA_20', None)
    self.data['entry_RSI'] = technicals.get('entry_RSI', None)
    self.data['entry_ADX'] = technicals.get('entry_ADX', None)
    self.data['entry_MACD'] = technicals.get('entry_MACD', None)
    self.data['entry_MACD_signal'] = technicals.get('entry_MACD_signal', None)
    self.data['entry_MACD_hist'] = technicals.get('entry_MACD_hist', None)
    self.data['entry_ATR'] = technicals.get('entry_ATR', None)
    self.data['entry_BB_UPPER'] = technicals.get('entry_BB_UPPER', None)
    self.data['entry_BB_MIDDLE'] = technicals.get('entry_BB_MIDDLE', None)
    self.data['entry_BB_LOWER'] = technicals.get('entry_BB_LOWER', None)
    self.data['entry_OBV'] = technicals.get('entry_OBV', None)
    self.data['exit_SMA_10'] = technicals.get('exit_SMA_10', None)
    self.data['exit_EMA_20'] = technicals.get('exit_EMA_20', None)
    self.data['exit_RSI'] = technicals.get('exit_RSI', None)
    self.data['exit_ADX'] = technicals.get('exit_ADX', None)
    self.data['exit_MACD'] = technicals.get('exit_MACD', None)
    self.data['exit_MACD_signal'] = technicals.get('exit_MACD_signal', None)
    self.data['exit_MACD_hist'] = technicals.get('exit_MACD_hist', None)
    self.data['exit_ATR'] = technicals.get('exit_ATR', None)
    self.data['exit_BB_UPPER'] = technicals.get('exit_BB_UPPER', None)
    self.data['exit_BB_MIDDLE'] = technicals.get('exit_BB_MIDDLE', None)
    self.data['exit_BB_LOWER'] = technicals.get('exit_BB_LOWER', None)
    self.data['exit_OBV'] = technicals.get('exit_OBV', None)
      
    # self.data['portfolio'] = self.data['position'] * self.data['Close'] * (1 - self.option_cost) + self.data['cash']
      # Portfolio value is now just cash (which includes P&L from closed positions)
    self.data['portfolio'] = self.data['cash']
    self.data = calcReturns(self.data)
      
# def calcReturns(df):
#       print('inside calcReturns')
#       df['returns'] = df['Close'] / df['Close'].shift(1)
#       df['log_returns'] = np.log(df['returns'])
#       df['strat_returns'] = df['portfolio'] / df['portfolio'].shift(1)
#       df['strat_log_returns'] = np.log(df['strat_returns'])
#       df['cum_returns'] = np.exp(df['log_returns'].cumsum()) - 1
#       df['strat_cum_returns'] = np.exp(df['strat_log_returns'].cumsum()) - 1
#       df['peak'] = df['cum_returns'].cummax()
#       df['strat_peak'] = df['strat_cum_returns'].cummax()
#       df['trade_num'] = np.nan
#       trades = df['position'].diff()
#       trade_start = df.index[np.where((trades!=0) & (df['position']!=0))]
#       trade_end = df.index[np.where((trades!=0) & (df['position']==0))]
#       df['trade_num'].loc[df.index.isin(trade_start)] = np.arange(
#           trade_start.shape[0])
#       df['trade_num'] = df['trade_num'].ffill()
#       df['trade_num'].loc[(df.index.isin(trade_end+timedelta(1))) & 
#                           (df['position']==0)] = np.nan
#       return df

# def calcReturns(df):
#     print('inside calcReturns')
#     # Calculate strategy returns based on portfolio value changes
#     df['strat_returns'] = df['portfolio'] / df['portfolio'].shift(1)
#     df['strat_log_returns'] = np.log(df['strat_returns'])
#     df['strat_cum_returns'] = np.exp(df['strat_log_returns'].cumsum()) - 1
#     df['strat_peak'] = df['strat_cum_returns'].cummax()
    
#     # Add trade numbering
#     df['trade_num'] = np.nan
#     trades = df['position'].diff()
#     trade_start = df.index[np.where((trades!=0) & (df['position']!=0))]
#     trade_end = df.index[np.where((trades!=0) & (df['position']==0))]
    
#     df['trade_num'].loc[df.index.isin(trade_start)] = np.arange(
#         trade_start.shape[0])
#     df['trade_num'] = df['trade_num'].ffill()
#     df['trade_num'].loc[(df.index.isin(trade_end+timedelta(1))) & 
#                         (df['position']==0)] = np.nan
    
#     return df

# def calcReturns(df):
#     print('inside calcReturns')
    
#     # Calculate daily portfolio returns
#     df['strat_returns'] = df['portfolio'] / df['portfolio'].shift(1)
#     df['strat_log_returns'] = np.log(df['strat_returns'])
#     df['strat_cum_returns'] = np.exp(df['strat_log_returns'].cumsum()) - 1
    
#     # Calculate underlying asset returns (for buy & hold comparison)
#     df['returns'] = df['Close'] / df['Close'].shift(1)
#     df['log_returns'] = np.log(df['returns'])
#     df['cum_returns'] = np.exp(df['log_returns'].cumsum()) - 1
    
#     # Track peaks for drawdown calculations
#     df['peak'] = df['cum_returns'].cummax()
#     df['strat_peak'] = df['strat_cum_returns'].cummax()
    
#     # Track trade numbers
#     df['trade_num'] = np.nan
#     trades = df['position'].diff()
#     trade_start = df.index[np.where((trades!=0) & (df['position']!=0))]
#     trade_end = df.index[np.where((trades!=0) & (df['position']==0))]
    
#     if len(trade_start) > 0:
#         df['trade_num'].loc[df.index.isin(trade_start)] = np.arange(
#             trade_start.shape[0])
#         df['trade_num'] = df['trade_num'].ffill()
#         df['trade_num'].loc[(df.index.isin(trade_end+timedelta(1))) & 
#                             (df['position']==0)] = np.nan
    
#     return df
    
def time_range(start, end, delta):
    current = start
    while current <= end:
        yield current
        current += delta
# def getStratStats(log_returns: pd.Series, risk_free_rate = 0.02):
#     stats = {}  # Total Returns
#     if log_returns.empty:
#         print("Warning: log_returns is empty. Cannot calculate statistics.")
#         return stats
#     stats['tot_returns'] = np.exp(log_returns.sum()) - 1 
#     stats['annual_returns'] = np.exp(log_returns.mean() * 252) - 1 
#     stats['annual_volatility'] = log_returns.std() * np.sqrt(252)
#     downside_returns = log_returns.loc[log_returns<0]
#     if not downside_returns.empty:
#         annualized_downside = downside_returns.std() * np.sqrt(252)
#         stats['sortino_ratio'] = (stats['annual_returns'] - risk_free_rate) / annualized_downside
#     else:
#         stats['sortino_ratio'] = np.nan
#     stats['sharpe_ratio'] = (stats['annual_returns'] - risk_free_rate) / stats['annual_volatility']  
#     cum_returns = log_returns.cumsum() - 1
#     peak = cum_returns.cummax()
#     drawdown = peak - cum_returns
#     if not drawdown.empty:
#         max_idx = drawdown.argmax()
#         stats['max_drawdown'] = 1 - np.exp(cum_returns[max_idx]) / np.exp(peak[max_idx])
#     else:
#         stats['max_drawdown'] = np.nan
#     strat_dd = drawdown[drawdown==0]
#     if len(strat_dd) > 1:
#         strat_dd_diff = strat_dd.index[1:] - strat_dd.index[:-1]
#         strat_dd_days = strat_dd_diff.map(lambda x: x.days).values
#         if len(drawdown) > 0 and len(strat_dd) > 0:
#             strat_dd_days = np.hstack([strat_dd_days, (drawdown.index[-1] - strat_dd.index[-1]).days])
#         stats['max_drawdown_duration'] = strat_dd_days.max()
#     else:
#         stats['max_drawdown_duration'] = np.nan

#     return {k: np.round(v, 4) if isinstance(v, (float, np.float_)) else v
#             for k, v in stats.items()}


# def calcReturns(df):
#     """
#     Simple P&L calculation for intraday options trading
#     """
#     # Calculate P&L based on portfolio value changes
#     df['daily_pnl'] = df['portfolio'].diff()
#     df['cumulative_pnl'] = df['portfolio'] - df['portfolio'].iloc[0]
    
#     # For stats compatibility, keep minimum required returns
#     df['strat_returns'] = df['portfolio'] / df['portfolio'].shift(1)
#     df['strat_log_returns'] = np.log(df['strat_returns'])
#     df['log_returns'] = np.log(df['Close'] / df['Close'].shift(1))
    
#     return df

# def getStratStats(log_returns: pd.Series, risk_free_rate=0.02):
#     """
#     Simple P&L statistics for intraday trading
#     """
#     stats = {}
#     if log_returns.empty:
#         print("Warning: No trading data available")
#         return stats
    
#     # Just calculate total P&L
#     total_return = np.exp(log_returns.sum()) - 1
#     stats['Total P&L'] = total_return * 100  # Convert to percentage
    
#     return {k: np.round(v, 4) if isinstance(v, (float, np.float_)) else v
#             for k, v in stats.items()}

def calcReturns(df):
    """ Log trade details to Excel for each strategy run and calculate returns """
    import pandas as pd
    import numpy as np
    from datetime import datetime
    import os
    excel_file = 'trade_log.xlsx'
    
    # Check if there was any trade in this run
    if 'pnl' in df.columns and not df['pnl'].isna().all():
        # Get non-zero PnL rows
        trade_rows = df[df['pnl'] != 0].dropna()
        
        if not trade_rows.empty:
            trade_data = []
            last_trade = trade_rows.iloc[-1]
            
            # Calculate exit price using iloc for integer-based indexing
            last_trade_idx = trade_rows.index[-1]
            df_idx = df.index.get_loc(last_trade_idx)
            
            # Calculate exit price safely using integer indexing
            if df_idx > 0:
                exit_price = df['portfolio'].iloc[df_idx] - df['portfolio'].iloc[df_idx - 1]
            else:
                exit_price = df['portfolio'].iloc[df_idx]
            
            trade_info = {
                'Date': datetime.now().strftime('%Y-%m-%d'),
                'Ticker': getattr(df, 'ticker', last_stock_traded),  # Get from global if attr not available
                'Option Type': last_trade['option_type'],
                'Option Underying Price': last_trade['option_underlying_price'],
                'Option Entry Underlying Price': last_trade['option_entry_underlying_price'],
                'Option Entry Strike': last_trade['option_entry_strike'],
                'Option Lot size': last_trade['option_lot_size'],
                'Entry Time': last_trade['option_entry_time'],
                'Entry Price': last_trade['option_entry_price'],
                'Exit Time': last_trade['option_exit_time'],
                'Exit Price': last_trade['option_exit_price'],
                'PnL': last_trade['pnl'],
                'Exit Reason': last_trade['option_exit_reason'],
                'Entry SMA_10': last_trade.get('entry_SMA_10', None),
                'Entry EMA_20': last_trade.get('entry_EMA_20', None),
                'Entry RSI': last_trade.get('entry_RSI', None),
                'Entry ADX': last_trade.get('entry_ADX', None),
                'Entry MACD': last_trade.get('entry_MACD', None),
                'Entry MACD_signal': last_trade.get('entry_MACD_signal', None),
                'Entry MACD_hist': last_trade.get('entry_MACD_hist', None),
                'Entry ATR': last_trade.get('entry_ATR', None),
                'Entry BB_UPPER': last_trade.get('entry_BB_UPPER', None),
                'Entry BB_MIDDLE': last_trade.get('entry_BB_MIDDLE', None),
                'Entry BB_LOWER': last_trade.get('entry_BB_LOWER', None),
                'Entry OBV': last_trade.get('entry_OBV', None),
                'Exit SMA_10': last_trade.get('exit_SMA_10', None),
                'Exit EMA_20': last_trade.get('exit_EMA_20', None),
                'Exit RSI': last_trade.get('exit_RSI', None),
                'Exit ADX': last_trade.get('exit_ADX', None),
                'Exit MACD': last_trade.get('exit_MACD', None),
                'Exit MACD_signal': last_trade.get('exit_MACD_signal', None),
                'Exit MACD_hist': last_trade.get('exit_MACD_hist', None),
                'Exit ATR': last_trade.get('exit_ATR', None),
                'Exit BB_UPPER': last_trade.get('exit_BB_UPPER', None),
                'Exit BB_MIDDLE': last_trade.get('exit_BB_MIDDLE', None),
                'Exit BB_LOWER': last_trade.get('exit_BB_LOWER', None),
                'Exit OBV': last_trade.get('exit_OBV', None)
            }
            trade_data.append(trade_info)
            
            # Create DataFrame for new trade
            new_trade_df = pd.DataFrame(trade_data)
              # If file exists, check for matching rows with the previous row before appending
            if os.path.exists(excel_file):
                existing_df = pd.read_excel(excel_file)
                if len(existing_df) > 0:  # Ensure there's at least one row to compare with
                    last_row = existing_df.iloc[-1]  # Get the last row
                    if (
                        last_row['Entry Time'] == trade_info['Entry Time']
                        and last_row['Ticker'] == trade_info['Ticker']
                        and last_row['Option Type'] == trade_info['Option Type']
                    ):
                        print("Skipping adding the trade data as it is a duplicate entry")
                         # Keep minimum required returns calculations for compatibility
                        df['strat_returns'] = df['portfolio'] / df['portfolio'].shift(1)
                        df['strat_log_returns'] = np.log(df['strat_returns'].replace([np.inf, -np.inf], np.nan))
                        df['log_returns'] = np.log(df['Close'] / df['Close'].shift(1))
                        return df  # Exit the function early
            
            try:
                # If file exists, append to it
                if os.path.exists(excel_file):
                    with pd.ExcelWriter(excel_file, mode='a', if_sheet_exists='overlay') as writer:
                        # Get the last row of existing data
                        existing_df = pd.read_excel(excel_file)
                        start_row = len(existing_df) + 1
                        new_trade_df.to_excel(writer, startrow=start_row, index=False, header=False)
                else:
                    new_trade_df.to_excel(excel_file, index=False)
                
                print(f"Trade logged successfully for {trade_info['Ticker']}")
                print(f"PnL: {trade_info['PnL']:.2f}")
                
                # Calculate cumulative PnL from Excel
                if os.path.exists(excel_file):
                    all_trades_df = pd.read_excel(excel_file)
                    cumulative_pnl = all_trades_df['PnL'].sum()
                    print(f"Cumulative PnL: {cumulative_pnl:.2f}")
            
            except Exception as e:
                print(f"Error writing to Excel: {e}")
    
    # Keep minimum required returns calculations for compatibility
    df['strat_returns'] = df['portfolio'] / df['portfolio'].shift(1)
    df['strat_log_returns'] = np.log(df['strat_returns'].replace([np.inf, -np.inf], np.nan))
    df['log_returns'] = np.log(df['Close'] / df['Close'].shift(1))
    return df

def getStratStats(log_returns: pd.Series, risk_free_rate=0.02):
    """ Calculate strategy statistics including total P&L from Excel """
    import os
    stats = {}
    
    if log_returns.empty:
        return stats
    
    # Calculate returns-based metrics
    stats['Strategy Returns'] = np.exp(log_returns.sum()) - 1
    
    # Add cumulative PnL from Excel if available
    excel_file = 'trade_log.xlsx'
    if os.path.exists(excel_file):
        try:
            trades_df = pd.read_excel(excel_file)
            stats['Total P&L'] = trades_df['PnL'].sum()
            stats['Number of Trades'] = len(trades_df)
            stats['Average P&L per Trade'] = stats['Total P&L'] / stats['Number of Trades']
            
            # Add win rate calculation
            winning_trades = len(trades_df[trades_df['PnL'] > 0])
            stats['Win Rate'] = winning_trades / len(trades_df) if len(trades_df) > 0 else 0
            
        except Exception as e:
            print(f"Error reading Excel for stats: {e}")
            stats['Total P&L'] = 0
            stats['Number of Trades'] = 0
            stats['Average P&L per Trade'] = 0
            stats['Win Rate'] = 0
    
    return {k: np.round(v, 4) if isinstance(v, (float, np.float_)) else v 
            for k, v in stats.items()}
    
import datetime as datetime
import datetime
import io
from docx import Document
import traceback
import linecache
import re
from collections import defaultdict
# ticker = 'MARUTI'
# search_ticker=ticker+'-EQ'
# exchange = 'NSE'
# ret9=api.searchscrip(exchange='NSE', searchtext=search_ticker)
# print(ret9['values'][0]['token'])
# date = '08-11-2024'
# tokenid = ret9['values'][0]['token']
# market_start = datetime.datetime.strptime('12:00', '%H:%M')
# market_end = datetime.datetime.strptime('15:30', '%H:%M')
# output = io.StringIO()
# doc = Document()
# delta = datetime.timedelta(minutes=1)
# window = datetime.timedelta(hours=1, minutes=30)
# trading_system = None
# import winsound
# for current_time in time_range(market_start, market_end, delta):
#     try:
#         print('inside main loop')
#         print(current_time)
#         start_time = max(current_time - window, market_start)
#         end_time = min(current_time, market_end)
#         start = start_time.strftime('%H:%M')
#         end = end_time.strftime('%H:%M')
#         print(f"Checking conditions for start: {start}, end: {end}")
#         conditions_met, signal = check_sideways_and_nadarya(ticker, tokenid, exchange, start, end, date)
#         print(f'signal', signal)
#         if conditions_met:
#             print(f"Conditions met. Running StarterSystem for start: {start}, end: {end}")
#             winsound.Beep(1000, 500)
#             trading_system = StarterSystem(ticker, exchange, start, end, date, tokenid)
#             trading_system.run()
#         else:
#             print(f"Conditions not met for start: {start}, end: {end}. Skipping.")
#             continue
#     except Exception as e:
#         error_info = traceback.extract_tb(e.__traceback__)[-1]
#         line_number = error_info.lineno
#         filename = error_info.filename
#         error_line = linecache.getline(filename, line_number).strip()
#         print(f"Error occurred for time range {start} - {end}: {str(e)}")
#         print(f"Error occurred on line {line_number}: {error_line}")
#         continue     

# print(trading_system)

def extract_symbol_details(symbol):
    """
    Extract the base symbol from option symbols like 'AARTIIND28NOV24C520'
    Returns the base symbol (e.g., 'AARTIIND')
    """
    match = re.match(r'^([A-Z]+)', symbol)
    if match:
        return match.group(1)
    return None

def get_script_details(api, symbol):
    """
    Search for script details using the API
    Returns token and trading symbol
    """
    try:
        search_text = f"{symbol}-EQ"
        ret = api.searchscrip(exchange='NSE', searchtext=search_text)
        if ret['values']:
            return {
                'token': ret['values'][0]['token'],
                'tsym': ret['values'][0]['tsym']
            }
        return None
    except Exception as e:
        print(f"Error searching for {symbol}: {str(e)}")
        return None

def process_options_list(api, options_list, date):
    """
    Process a list of options and run the trading system for each unique base symbol
    """
    # Group options by base symbol
    symbol_groups = defaultdict(list)
    for option in options_list:
        base_symbol = extract_symbol_details(option)
        if base_symbol:
            symbol_groups[base_symbol].append(option)
    
    results = []
    processed_symbols = set()  # Keep track of processed symbols
    # global last_stock_traded
        # last_stock_traded=self.ticker
    # global last_stock_traded_time
        # last_stock_traded_time=exit_time
    # global stop_loss_reason
        # stop_loss_reason=reason
    global reversed_signal
    reversed_signal = 0
    
    for base_symbol, related_options in symbol_groups.items():
        if base_symbol in processed_symbols:
            print(f"Skipping {base_symbol} as it was already processed")
            continue
            
        print(f"Processing {base_symbol} (Options: {', '.join(related_options)})")
        
        script_details = get_script_details(api, base_symbol)
        if not script_details:
            print(f"Could not find script details for {base_symbol}")
            continue
            
        tokenid = script_details['token']
        
        # Trading time parameters
        market_start = datetime.datetime.strptime('12:00', '%H:%M')
        market_end = datetime.datetime.strptime('15:30', '%H:%M')
        delta = datetime.timedelta(minutes=1)
        window = datetime.timedelta(hours=1, minutes=30)
        
        output = io.StringIO()
        doc = Document()
        trading_system = None
        
        try:
            for current_time in time_range(market_start, market_end, delta):
                # print(f'Processing {base_symbol} at {current_time}')
                # print(f'window', window)
                # print(f'current_time',current_time)
                start_1 = current_time.strftime('%H:%M')
                # print(f'start_1',start_1)
                # time_str = current_time.split()[1]  # Get the time part
            
                # Create a datetime object with the extracted time
                time_obj = datetime.datetime.strptime(start_1, '%H:%M').time()
                start_time = max(current_time - window, market_start)
                end_time = min(current_time, market_end)
                start = start_time.strftime('%H:%M')
                end = end_time.strftime('%H:%M')
                # from datetime import datetime
                # time_str = selected_option['time'].split()[1]  # Get the time part
                    
                    # Create a datetime object with the extracted time
                # time_obj_1 = datetime.datetime.strptime(start, '%H:%M').time()
                # print(f'time_obj',time_obj)
                    # Check if the time is greater than 15:00:00
                if time_obj > datetime.datetime.strptime('15:00:00', '%H:%M:%S').time():
                        x=1
                        # print("Time is greater than 15:00:00")
                        continue

                # print(f'stop_loss_reason -1',stop_loss_reason)
                # print(f'reversed_signal -1', reversed_signal)
                
                # print(f'last_stock_traded',last_stock_traded)
                # print(f'last_stock_traded_time',last_stock_traded_time)
                if 'last_stock_traded' in globals() and 'last_stock_traded_time' in globals():
                    if base_symbol == last_stock_traded:
                        last_traded_time = last_stock_traded_time.strftime('%H:%M')
                        if last_traded_time > end:
                            x=1
                            # print(f'start',start)
                            # print(f'end',end)
                            # print(f"Skipping {base_symbol} as it was traded recently at {last_stock_traded_time}")
                            continue

                if 'stop_loss_reason' in globals():
                    if base_symbol == last_stock_traded:
                        if stop_loss_reason  == 'Initial stop loss hit':
                            print(f"Conditions met for {base_symbol} due to initial stop loss. Running StarterSystem")
                            winsound.Beep(1000, 500)
                            trading_system = StarterSystem(
                                base_symbol, 'NSE', start, end, date, tokenid
                            )
                            trading_system.run(reversed_signal)
                            
                
                # print(f"Checking conditions for {base_symbol} - start: {start}, end: {end}")
                
                conditions_met, signal = check_sideways_and_nadarya(
                    base_symbol, tokenid, 'NSE', start, end, date
                )
                
                # print(f'Signal for {base_symbol}: {signal}')
                
                if conditions_met and signal!=0:
                    global reversed_signal_dummy
                    reversed_signal_dummy = 0
                    if 'last_stock_traded' in globals() and 'last_stock_traded_time' in globals():
                        if base_symbol == last_stock_traded:
                            last_traded_time = last_stock_traded_time.strftime('%H:%M')
                            if last_traded_time > end:
                                x=1
                                # print(f'start',start)
                                # print(f'end',end)
                                # print(f"Skipping {base_symbol} as it was traded recently at {last_stock_traded_time}")
                                continue
                    print(f"Conditions met for {base_symbol}. Running StarterSystem")
                    # print(f'last_stock_traded',last_stock_traded)
                    # print(f'last_stock_traded_time',last_stock_traded_time)
                    winsound.Beep(1000, 500)
                    trading_system = StarterSystem(
                        base_symbol, 'NSE', start, end, date, tokenid
                    )
                    trading_system.run(reversed_signal_dummy)
                    # print(f'last_stock_traded',last_stock_traded)
                    # print(f'last_stock_traded_time', last_stock_traded_time)
                else:
                    x=1
                    # print(f"Conditions not met for {base_symbol} at {start}-{end}")
                    continue

            # print(trading_system)
            # if trading_system is not None:
            #     df = calcReturns(trading_system.data)
            #     stats = pd.DataFrame(
            #         getStratStats(df['strat_log_returns']),
            #         index=['Strategy'])
            #     # print(f"\nTrading Summary:")
            #     # print(f"Total P&L: ₹{df['cumulative_pnl'].iloc[-1]:.2f}")
            # else:
            #     print("No trades were executed or no valid data available for statistics calculation")

                    
        except Exception as e:
            error_info = traceback.extract_tb(e.__traceback__)[-1]
            line_number = error_info.lineno
            filename = error_info.filename
            error_line = linecache.getline(filename, line_number).strip()
            print(f"Error processing {base_symbol}: {str(e)}")
            print(f"Error occurred on line {line_number}: {error_line}")
            continue
            
        processed_symbols.add(base_symbol)  # Mark this symbol as processed
        
        results.append({
            'symbol': base_symbol,
            'options': related_options,
            'trading_system': trading_system
        })
    
    return results

# Example usage
# options_list = new_list_nfo
options_list = ['BATAINDIA31JUL2025']
date = '20-06-2025'

# Run the processing
results = process_options_list(api, options_list, date)

# # Print results
# for result in results:
#     print(f"Results for {result['option']}:")
#     print(f"Base symbol: {result['symbol']}")
#     print(f"Trading system status: {result['trading_system']}")
#     print("-" * 50)

# df = calcReturns(df)
# strat_stats = pd.DataFrame(
#             getStratStats(sys.data['strat_log_returns']),
#             index=['Strategy'])
# buy_hold_stats = pd.DataFrame(
#             getStratStats(sys.data['log_returns']),
#             index=['Buy and Hold'])
# stats = pd.concat([strat_stats, buy_hold_stats])
# print(stats)

# After the loop, calculate returns on sys.data
# print(trading_system)
# if trading_system is not None:
#     # Calculate returns using the calcReturns function
#     df = calcReturns(trading_system.data)
    
#     # Now calculate the statistics using the processed DataFrame
#     strat_stats = pd.DataFrame(
#         getStratStats(df['strat_log_returns']),
#         index=['Strategy'])
#     buy_hold_stats = pd.DataFrame(
#         getStratStats(df['log_returns']),
#         index=['Buy and Hold'])
#     stats = pd.concat([strat_stats, buy_hold_stats])
#     print(stats)
# else:
#     print("No trades were executed or no valid data available for statistics calculation")

# hours = 10
# interval = 5
# output_text = output.getvalue()
# doc.add_paragraph(output_text)
# doc.save('output_nadarya_backtest.docx')
# print("Output has been saved to output.docx")
print('completed')



